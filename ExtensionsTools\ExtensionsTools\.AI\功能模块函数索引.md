# ExtensionsTools 功能模块函数索引

## 📋 索引说明
本索引为ExtensionsTools目录下所有public方法的快速查找工具，按功能模块分类整理，方便AI编程工具快速定位已有功能。

---

## 🔧 核心工具类模块

#### ETString - 字符串处理扩展
**路径**: ExtensionsTools/ExtensionsTools/ETString.cs
**功能**: 提供丰富的字符串处理和操作功能
**主要类**: ETString (静态扩展类)
**核心方法**:
- RemoveRepeatedChars(string, string) - 移除重复字符
- RemoveRepeatedChars(string, List<char>) - 移除重复字符
- RemoveAdjacentDuplicateChars(string) - 移除相邻重复字符
- RemoveDuplicates(List<string>) - 移除重复项
- StartWithInArray(string, string[]) - 检查开头匹配
- EndWithInArray(string, string[]) - 检查结尾匹配
- ContainsAny(string, IEnumerable<string>) - 检查包含任一项
- Contains(string, string) - 包含检查
- ContainsAll<T>(IEnumerable<T>, IEnumerable<T>) - 检查包含全部
- ByteLength(string) - 获取字节长度
- ToEn(string) - 转换为英文
- ToDBC(string) - 转全角字符
- ToList(string, string) - 分割为列表
- Split(string, string) - 字符串分割
- ToTable(string, string) - 转换为表格
- ToHashset(string, string) - 转换为哈希集
- GetRightPart(string, string) - 获取右侧部分
- GetLeftPart(string, string) - 获取左侧部分
- GetLeftByByte(string, int) - 按字节获取左侧
- GetFirstQuotedString(string) - 获取首个引号内容
- GetFirstSingleQuotedString(string) - 获取首个单引号内容
- GetFirstSplitCharInsideString(string, char) - 获取首个分割字符
- GetBracketContent(string, string) - 获取括号内容
- GetLineWithMostCharacters(string) - 获取最长行
- RemoveEmptyLines(string, bool) - 移除空行
- RemoveFirstLineIfStartsWith(string, string) - 移除首行条件
- GetFirstLine(string) - 获取首行
- GetContentBetweenBrackets(string, string) - 获取括号间内容
- Append(string, string, string) - 追加字符串
- RegexReplace(string, string, string) - 正则替换
- RegexMatchText(string, string, int) - 正则匹配文本
- GetMd5Str(string) - 获取MD5哈希
- RegularEnter(string, bool) - 规范换行符
- ConvertToSplitChar(string) - 转换分割字符
- GetClipboardText(int) - 获取剪贴板文本
- GetTextWidth(string, string, float) - 获取文本宽度
- LevenshteinDistance(string, string, bool) - 计算编辑距离
- IsDateTime(string) - 检查日期时间格式

#### ETString2 - 字符串处理扩展2
**路径**: ExtensionsTools/ExtensionsTools/ETString2.cs
**功能**: 提供额外的字符串处理和验证功能
**主要类**: ETString2 (静态扩展类)
**核心方法**:
- IsNullOrWhiteSpace(string) - 检查空白字符
- IsNullOrEmpty(string) - 检查空字符串
- IfEmpty(string, string) - 空值默认处理
- RemoveWhiteSpace(string) - 移除空白字符
- RemoveChars(string, char[]) - 移除指定字符
- RemoveNumbers(string) - 移除数字
- RemoveLetters(string) - 移除字母
- RemovePunctuation(string) - 移除标点符号
- GetNumbers(string) - 获取数字
- GetLetters(string) - 获取字母
- GetChinese(string) - 获取汉字
- RemoveChinese(string) - 移除汉字
- RemoveString(string, string) - 移除指定字符串
- RemoveStrings(string, string[]) - 移除多个字符串
- RemoveHtmlTags(string) - 移除HTML标签
- RemoveNewLines(string) - 移除换行符
- ToCamelCase(string) - 转驼峰命名
- ToPascalCase(string) - 转帕斯卡命名
- ToSnakeCase(string) - 转下划线命名
- ToKebabCase(string) - 转短横线命名

#### ETStringPrefixSuffixProcessor - 字符串前后缀处理
**路径**: ExtensionsTools/ExtensionsTools/ETStringPrefixSuffixProcessor.cs
**功能**: 处理字符串的前缀和后缀移除操作
**主要类**: ETStringPrefixSuffixProcessor (静态类)
**核心方法**:
- Initialize() - 初始化配置
- RemovePrefix(string, bool) - 移除前缀
- RemoveSuffix(string, bool) - 移除后缀
- RemovePrefixAndSuffix(string) - 移除前后缀
- OpenPrefixSuffixConfigFile() - 打开配置文件
- ReloadConfig() - 重新加载配置

#### ETTime - 时间处理扩展
**路径**: ExtensionsTools/ExtensionsTools/ETTime.cs
**功能**: 提供文件时间修改和时间处理功能
**主要类**: ETTime (静态类)
**核心方法**:
- ModifyFileTime(string, DateTime, DateTime, DateTime) - 修改文件时间

#### ETFile - 文件操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETFile.cs
**功能**: 提供丰富的文件和目录操作功能
**主要类**: ETFile (静态扩展类)
**核心方法**:
- PathRemoveInvalidChars(string, string) - 移除非法字符
- ChangeExtension(string, string) - 更改扩展名
- RelativePath(string, string) - 获取相对路径
- ApplicationPath(string, PathType, string) - 获取应用路径
- ZipDirectory(string, string) - 压缩目录
- ZipDirectory(string) - 压缩目录简化版
- ZipSubDirectories(string) - 压缩子目录
- ExtractArchive(string, string, bool) - 解压缩文件
- DetectArchiveType(string) - 检测压缩格式
- DirectoryAllFiles(string, string) - 获取目录文件
- FileIsNewer(string, string) - 比较文件新旧
- FileLastWriteTime(string) - 获取写入时间
- FileLastWriteTime(string, DateTime) - 设置写入时间
- FileCopyToClipboard(string) - 复制到剪贴板
- FileCopyToClipboard(FileInfo) - 复制到剪贴板
- FileSetReadOnly(string) - 设置只读
- FileSetReadOnly(FileInfo) - 设置只读
- PathAddLongPathPrefix(string) - 添加长路径前缀
- PathAdjustNameLength(string) - 调整路径长度
- FileCopy(string, string, bool) - 文件复制
- FileDelete(string) - 文件删除
- FileExists(string) - 检查文件存在
- DirectoryExists(string) - 检查目录存在
- ReadFileToArray(string) - 读取文件到数组
- FileAppendText(string, string) - 追加文本到文件

#### ETForm - 窗体操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETForm.cs
**功能**: 提供丰富的WinForms窗体和控件操作功能
**主要类**: ETForm (静态扩展类)
**核心方法**:
- SendMessageToWindow(string, string) - 发送消息到窗口
- HideToTray(Form) - 隐藏到托盘
- Hide(Form) - 隐藏窗体
- HideToTray(Form, FormClosingEventArgs) - 关闭时隐藏到托盘
- NormalWindowState(Form) - 设置正常窗口状态
- PositionFormAtScreenBottomRight(Form, int, int) - 定位到右下角
- NoActivateStyle(Form) - 设置无激活样式
- AsChildWindow(Form, IntPtr) - 设为子窗口
- WorksheetWorkingAreaRect(Application) - 获取工作区矩形
- RangeRect(Application, Range) - 获取范围矩形
- PointsToScreenPixels(double) - 点数转屏幕像素
- ActiveExcelAppHwnd() - 激活Excel窗口
- SaveExcelAppHwnd() - 保存Excel窗口句柄
- IsExcelWindow(IntPtr) - 检查Excel窗口
- ListBoxSelectedItemToString(ListBox, string) - 列表框选中项转字符串
- ListBoxSelectedItem(ListBox) - 获取列表框选中项
- LoadComboBox(ComboBox, IEnumerable<string>) - 加载下拉框
- LoadComboBox(ComboBox, string, string) - 从路径加载下拉框
- LoadListBox(ListBox, IEnumerable<string>, string) - 加载列表框
- LoadCheckedListBox(CheckedListBox, IEnumerable<string>, string) - 加载复选列表框
- LoadContextMenuStrip(ContextMenuStrip, Dictionary<string, string[]>, TextBox) - 加载上下文菜单
- CheckedListBoxItemInvalid(CheckedListBox, string) - 复选列表框项无效化
- AddMessageToListview(ListView, string, bool) - 添加消息到列表视图
- HideTabPage(TabControl, TabPage) - 隐藏标签页
- ShowTabPage(TabControl, TabPage) - 显示标签页
- EnabledInvoke(Control, bool) - 线程安全启用控件
- VisibleInvoke(Control, bool) - 线程安全设置可见性
- TextInvoke(Button, string) - 线程安全设置按钮文本
- ClearInvoke(TextBox) - 线程安全清空文本框
- WriteLogInvoke(TextBox, string) - 线程安全写入日志
- BindWindowsFormControl(dynamic, ETIniFile, string, string) - 绑定控件到INI
- BindComboBox(ComboBox, ETIniFile, string, string) - 绑定下拉框到INI
- UpdateComboBoxItems(ComboBox) - 更新下拉框项
- UpdateComboBoxItemsAndSaveToIni(ComboBox, ETIniFile, string, string) - 更新并保存到INI
- ShowHide菜单子项(CommandBarControls, string[], bool, bool) - 显示隐藏菜单项
- ShowHide菜单子项(string[], bool) - 显示隐藏菜单项简化版
- AddMenuButton(CommandBarControls, string, string, int, bool) - 添加菜单按钮
- AddPopupButton(CommandBarControls, string, string, int, bool) - 添加弹出按钮
- FindMenuIndex(CommandBar, string) - 查找菜单索引
- WriteLog(TextBox, string, bool) - 写入日志到文本框
- AppendTextInvoke(TextBox, string, bool) - 追加文本到文本框
- SetParent(IntPtr, IntPtr) - 设置父窗口
- SetWindowLongPtr(IntPtr, int, IntPtr) - 设置窗口长指针
- FindWindow(string, string) - 查找窗口
- EnableWindow(IntPtr, bool) - 启用禁用窗口

#### ETFormater - 格式化工具
**路径**: ExtensionsTools/ExtensionsTools/ETFormater.cs
**功能**: 提供数字和金额格式化功能
**主要类**: ETFormater (静态类)
**核心方法**:
- ToChineseMoney(object) - 转换为中文大写金额

#### ETDateHelper - 日期助手
**路径**: ExtensionsTools/ExtensionsTools/ETDateHelper.cs
**功能**: 提供丰富的日期时间处理和计算功能
**主要类**: ETDateHelper (静态类)
**核心方法**:
- GetTimestampSeconds() - 获取时间戳秒
- GetTimestampMilliseconds() - 获取时间戳毫秒
- TimestampToDateTime(long) - 时间戳转日期时间
- TimestampMillisecondsToDateTime(long) - 毫秒时间戳转日期时间
- DateTimeToTimestamp(DateTime) - 日期时间转时间戳
- DateTimeToTimestampMilliseconds(DateTime) - 日期时间转毫秒时间戳
- FormatDateTime(DateTime, string) - 格式化日期时间
- TryParseDateTime(string, out DateTime) - 尝试解析日期时间
- TryParseDateTimeExact(string, string, out DateTime) - 精确解析日期时间
- IsDateTime(string) - 检查日期时间格式
- IsDateTimeFormat(string) - 检查日期时间格式
- IsValidDateFormat(string) - 检查有效日期格式
- IsValidTimeFormat(string) - 检查有效时间格式
- IsWorkday(DateTime) - 检查工作日
- IsWeekend(DateTime) - 检查周末
- GetWorkdaysCount(DateTime, DateTime) - 获取工作日数量
- GetFirstDayOfMonth(DateTime) - 获取月份第一天
- GetLastDayOfMonth(DateTime) - 获取月份最后一天
- GetFirstDayOfYear(DateTime) - 获取年份第一天
- GetLastDayOfYear(DateTime) - 获取年份最后一天
- GetFirstDayOfQuarter(DateTime) - 获取季度第一天
- GetLastDayOfQuarter(DateTime) - 获取季度最后一天
- GetFirstDayOfWeek(DateTime) - 获取周第一天
- GetLastDayOfWeek(DateTime) - 获取周最后一天
- AddWorkdays(DateTime, int) - 添加工作日
- GenerateRandomDateTime(DateTime, DateTime) - 生成随机日期时间
- CompareDateOnly(DateTime, DateTime) - 仅比较日期
- CompareTimeOnly(DateTime, DateTime) - 仅比较时间
- IsTimeInRange(TimeSpan, TimeSpan, TimeSpan) - 检查时间范围
- IsInWorkHours(DateTime, TimeSpan?, TimeSpan?) - 检查工作时间
- GetChineseLunarDate(DateTime) - 获取农历日期
- GetDayOfWeekName(DateTime, bool) - 获取星期名称
- GetMonthName(DateTime, bool) - 获取月份名称
- GetWeekOfMonth(DateTime) - 获取月中第几周
- GetWeekOfYear(DateTime) - 获取年中第几周

#### ETTextCrypto - 文本加密
**路径**: ExtensionsTools/ExtensionsTools/ETTextCrypto.cs
**功能**: 提供文本加密解密和压缩功能
**主要类**: ETTextCrypto (静态类)
**核心方法**:
- EncryptText(string, string) - 加密文本
- DecryptText(string, string) - 解密文本
- CompressAndEncryptText(string, string) - 压缩并加密文本
- DecryptAndDecompressText(string, string) - 解密并解压缩文本
- GetEncryptionKey(string) - 获取加密密钥

#### ETNet - 网络操作
**路径**: ExtensionsTools/ExtensionsTools/ETNet.cs
**功能**: 提供网络请求和HTML处理功能
**主要类**: ETNet (静态类)
**核心方法**:
- ConvertHtmlToJsonObject(string) - HTML转JSON对象
- GetHtmlFromUrl(string, int) - 从URL获取HTML

#### ETMail - 邮件操作
**路径**: ExtensionsTools/ExtensionsTools/ETMail.cs
**功能**: 提供邮件发送和匹配功能
**主要类**: ETMail (静态类)
**核心方法**:
- MatchMailSubjectAndKeyWord(string, string, HashSet<string>, HashSet<string>) - 匹配邮件主题关键词
- SendMessageEmail(string, string, string) - 发送邮件
- SendMessageEmail(string, string) - 发送邮件简化版

#### ETConfig - 配置管理
**路径**: ExtensionsTools/ExtensionsTools/ETConfig.cs
**功能**: 提供配置文件路径管理和处理功能
**主要类**: ETConfig (静态类)
**核心方法**:
- GetETConfigIniFilePath() - 获取配置INI文件路径
- GetConfigPath(string, string) - 获取配置路径
- ConfigFileToDictionary(string) - 配置文件转字典
- OpenConfigFile(string) - 打开配置文件

#### ETIniFile - INI文件操作
**路径**: ExtensionsTools/ExtensionsTools/ETIniFile.cs
**功能**: 提供完整的INI文件读写和对象序列化功能
**主要类**: ETIniFile (实例类)
**核心方法**:
- ETIniFile(string) - 构造函数
- IniReadValue(string, string) - 读取INI值
- IniWriteValue(string, string, string) - 写入INI值
- IniWriteFile() - 保存INI文件
- ReadInt(string, string, int) - 读取整数
- ReadBool(string, string, bool) - 读取布尔值
- ReadDateTime(string, string, DateTime) - 读取日期时间
- WriteInt(string, string, int) - 写入整数
- WriteBool(string, string, bool) - 写入布尔值
- WriteDateTime(string, string, DateTime) - 写入日期时间
- GetSectionNames() - 获取节名称列表
- GetSection(string) - 获取节内容
- DeleteSection(string) - 删除节
- DeleteKey(string, string) - 删除键
- SaveObject<T>(string, T) - 保存对象
- LoadObject<T>(string) - 加载对象
- LoadAllObjects<T>() - 加载所有对象
- GetValue(string, string, string) - 获取值兼容版
- GetBool(string, string, bool) - 获取布尔值兼容版
- SetValue(string, string, string) - 设置值兼容版
- Save() - 保存兼容版
- ReadString(string, string, string) - 读取字符串兼容版
- WriteString(string, string, string) - 写入字符串兼容版

#### ETInitializer - 初始化器
**路径**: ExtensionsTools/ExtensionsTools/ETInitializer.cs
**功能**: 提供产品授权和许可证管理功能
**主要类**: ETInitializer (实例类), LicenseConfig (配置类)
**核心方法**:
- LicenseConfig.CreateDefault(string) - 创建默认授权配置
- LicenseConfig.Create(string, string, string) - 创建授权配置
- ETInitializer(string) - 构造函数
- ETInitializer(string, string) - 构造函数带密钥
- ETInitializer(string, string, string) - 构造函数带远程URL
- ETInitializer(LicenseConfig) - 构造函数带配置
- InitializeLicenseController(LicenseConfig) - 初始化授权控制器
- HasPermissionAsync(string) - 异步检查权限
- HasPermission(string) - 检查权限
- CheckPermissionWithExpireTimeAsync(string) - 异步检查权限带过期时间
- CheckPermissionWithExpireTime(string) - 检查权限带过期时间
- OpenLicenseManager() - 打开授权管理窗体
- GetAuthorizedFeaturesAsync() - 异步获取授权功能列表
- GetAuthorizedFeaturesWithExpireTimeAsync() - 异步获取授权功能带过期时间
- SetCurrentUser(string, string[]) - 设置当前用户
- RefreshLicenseInfoAsync(bool) - 异步刷新授权信息
- GetCurrentMachineCode() - 获取当前机器码

#### ETLogManager - 日志管理
**路径**: ExtensionsTools/ExtensionsTools/ETLogManager.cs
**功能**: 提供完整的日志记录和管理功能
**主要类**: ETLogManager (静态类)
**核心方法**:
- ResetLogPathToDefault() - 重置日志路径为默认
- GetCurrentLogPath() - 获取当前日志路径
- SetLogSubFolder(string) - 设置日志子文件夹
- Info(string) - 写入信息日志
- Info(object, string) - 写入信息日志带源标识
- Info(string, Exception) - 写入信息日志带异常
- Info(object, string, Exception) - 写入信息日志带源、消息和异常
- Info(object, Exception) - 写入信息日志带源和异常
- Info(Exception) - 写入异常信息日志
- Info(ETException) - 写入ET异常信息日志
- Debug(string) - 写入调试日志
- Debug(object, string) - 写入调试日志带源标识
- Debug(string, Exception) - 写入调试日志带异常
- Debug(object, string, Exception) - 写入调试日志带源、消息和异常
- Debug(object, Exception) - 写入调试日志带源和异常
- Debug(Exception) - 写入异常调试日志
- Debug(ETException) - 写入ET异常调试日志
- Warning(string) - 写入警告日志
- Warning(object, string) - 写入警告日志带源标识
- Warning(string, Exception) - 写入警告日志带异常
- Warning(object, string, Exception) - 写入警告日志带源、消息和异常
- Warning(object, Exception) - 写入警告日志带源和异常
- Warning(Exception) - 写入异常警告日志
- Warning(ETException) - 写入ET异常警告日志
- Warn(string) - 写入警告日志(已过期)
- Error(string) - 写入错误日志
- Error(object, string) - 写入错误日志带源标识
- Error(string, Exception) - 写入错误日志带异常
- Error(object, string, Exception) - 写入错误日志带源、消息和异常
- Error(object, Exception) - 写入错误日志带源和异常
- Error(Exception) - 写入异常错误日志
- Error(ETException) - 写入ET异常错误日志
- WriteToTextBox(TextBox, string) - 写入到文本框(已过期)
- LogToTextBox(TextBox, string, string, bool) - 日志到文本框
- InfoToTextBox(TextBox, string, bool) - 信息日志到文本框
- DebugToTextBox(TextBox, string, bool) - 调试日志到文本框
- WarningToTextBox(TextBox, string, bool) - 警告日志到文本框
- ErrorToTextBox(TextBox, string, bool) - 错误日志到文本框
- ErrorToTextBox(TextBox, string, Exception, bool) - 错误日志带异常到文本框
- LogToMultipleTextBoxes(TextBox, TextBox, string, string, bool, bool) - 多文本框日志

#### ETException - 异常处理扩展
**路径**: ExtensionsTools/ExtensionsTools/ETException.cs
**功能**: 提供自定义异常类型和处理功能
**主要类**: ETException (异常类)
**核心方法**:
- ETException() - 默认构造函数
- ETException(bool) - 构造函数带消息框选项
- ETException(string, bool) - 构造函数带消息和消息框选项
- ETException(string, string, bool) - 构造函数带操作类型
- ETException(string, Exception, bool) - 构造函数带内部异常
- ETException(string, string, Exception, bool) - 构造函数完整版
- GetObjectData(SerializationInfo, StreamingContext) - 获取序列化数据

#### ETAutoCollapseWindowBehavior - 窗口自动折叠行为
**路径**: ExtensionsTools/ExtensionsTools/ETAutoCollapseWindowBehavior.cs
**功能**: 提供窗体自动折叠和展开行为控制
**主要类**: ETAutoCollapseWindowBehavior (实例类)
**核心方法**:
- ETAutoCollapseWindowBehavior(Form) - 构造函数
- Dispose() - 释放资源
- SetExpandedSize(int, int) - 设置展开尺寸

#### WinAPI - Windows API调用
**路径**: ExtensionsTools/ExtensionsTools/WinAPI.cs
**功能**: 提供Windows系统API调用功能
**主要类**: WinAPI (静态类)
**核心方法**:
- IsWindow(IntPtr) - 检查窗口有效性
- SetWindowPos(IntPtr, IntPtr, int, int, int, int, uint) - 设置窗口位置
- SetLayeredWindowAttributes(IntPtr, int, int, int) - 设置分层窗口属性
- FindWindow(string, string) - 查找窗口
- ShowWindow(IntPtr, int) - 显示窗口
- SetWindowRgn(IntPtr, IntPtr, bool) - 设置窗口区域
- MoveWindow(IntPtr, int, int, int, int, bool) - 移动窗口
- ReleaseDC(IntPtr, IntPtr) - 释放设备上下文
- GetDeviceCaps(IntPtr, int) - 获取设备能力
- GetDC(IntPtr) - 获取设备上下文
- FindWindowEx(IntPtr, IntPtr, string, string) - 查找子窗口
- OffsetRgn(IntPtr, int, int) - 偏移区域
- DeleteObject(IntPtr) - 删除对象
- CombineRgn(IntPtr, IntPtr, IntPtr, int) - 合并区域
- CreateRectRgn(int, int, int, int) - 创建矩形区域
- GetModuleFileNameEx(IntPtr, IntPtr, StringBuilder, uint) - 获取模块文件名
- SetWindowLong(IntPtr, int, uint) - 设置窗口长整型值
- GetWindowLong(IntPtr, int) - 获取窗口长整型值
- GetWindow(IntPtr, uint) - 获取窗口
- SetForegroundWindow(IntPtr) - 设置前台窗口
- GetWindowThreadProcessId(IntPtr, out int) - 获取窗口线程进程ID
- GetWindowRect(IntPtr, ref RECT) - 获取窗口矩形
- GetForegroundWindow() - 获取前台窗口
- SendMessage(IntPtr, int, int, ref COPYDATASTRUCT) - 发送消息
- GetSystemMetrics(int) - 获取系统度量
- GetWindowDC(IntPtr) - 获取窗口设备上下文

#### WinAPI_form - Windows API窗体扩展
**路径**: ExtensionsTools/ExtensionsTools/WinAPI_form.cs
**功能**: 提供窗体相关的Windows API调用
**主要类**: WinAPI_form (静态类)
**核心方法**:
- SetForegroundWindow(IntPtr) - 设置前台窗口
- GetWindowThreadProcessId(IntPtr, out int) - 获取窗口线程进程ID
- GetWindowRect(IntPtr, ref RECT) - 获取窗口矩形
- GetForegroundWindow() - 获取前台窗口
- GetActiveWindow() - 获取活动窗口
- SetActiveWindow(IntPtr) - 设置活动窗口
- GetWindowLongPtr(IntPtr, int) - 获取窗口长指针
- SetWindowLongPtr(IntPtr, int, IntPtr) - 设置窗口长指针
- SetParent(IntPtr, IntPtr) - 设置父窗口
- EnableWindow(IntPtr, bool) - 启用禁用窗口
- SendMessage(IntPtr, int, int, ref COPYDATASTRUCT) - 发送消息
- FindWindow(string, string) - 查找窗口

---

## 📊 Excel操作模块

#### ETExcelExtensions_Core - Excel核心功能
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Core.cs
**功能**: 提供Excel应用程序核心信息获取功能
**主要类**: ETExcelExtensions_Core (静态扩展类)
**核心方法**:
- GetExcelVersion() - 获取Excel版本
- IsOpenedFromOLE() - 检查OLE打开状态

#### ETExcelExtensions_AppControl - Excel应用控制
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_AppControl.cs
**功能**: 提供Excel应用程序状态和计算控制功能
**主要类**: ETExcelExtensions_AppControl (静态扩展类)
**核心方法**:
- Update(bool) - 更新Excel应用
- DisplayOutline(string) - 显示大纲
- AutoCalculation(bool) - 自动计算控制
- ForceRecalculateAndRestoreState() - 强制重算并恢复状态
- ScreenUpdate() - 屏幕更新控制
- Calculation() - 计算控制
- SetCalculationAutomatic() - 设置自动计算
- SetCalculationManual() - 设置手动计算
- SetAppNormalMode(bool) - 设置应用正常模式
- SetAppFastMode(bool) - 设置应用快速模式
- ResetCellsNotesSize(Worksheet) - 重置单元格注释大小

#### ETExcelExtensions_RangeOperations - 范围操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_RangeOperations.cs
**功能**: 提供Excel范围的复制、填充和批量操作功能
**主要类**: ETExcelExtensions_RangeOperations (静态扩展类)
**核心方法**:
- CopyRangeFormula(Range, Range, bool, bool) - 复制范围公式
- CopyRangeValues(Range, Range) - 复制范围值
- FillEmptyCells(Range, object, bool) - 填充空单元格
- BatchDeleteRowsByCriteria(Range, string, string) - 批量删除行
- GetRangeValue(Range) - 获取范围值
- SetRangeValue(Range, object) - 设置范围值

#### ETExcelExtensions_WorksheetOperations - 工作表操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_WorksheetOperations.cs
**功能**: 提供工作表复制、创建和管理功能
**主要类**: ETExcelExtensions_WorksheetOperations (静态扩展类)
**核心方法**:
- CopyWorksheetTo(Worksheet, Workbook) - 复制工作表到工作簿
- CopyWorksheetToNewWorkbook(Worksheet) - 复制工作表到新工作簿
- CopyWorksheetAfter(Worksheet, Worksheet, string) - 在指定位置后复制工作表
- CopyWorksheetFromTemplateToWorkbook(string, Workbook, string, Worksheet) - 从模板复制工作表
- CreateNewWorksheet(Workbook, string, Worksheet) - 创建新工作表

#### ETExcelExtensions_WorkbookOperations - 工作簿操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_WorkbookOperations.cs
**功能**: 提供工作簿打开、保存和另存功能
**主要类**: ETExcelExtensions_WorkbookOperations (静态扩展类)
**核心方法**:
- OpenWorkbook(string, bool) - 打开工作簿
- Save(Workbook) - 保存工作簿
- SaveAs(Workbook, string, bool, bool) - 另存为工作簿
- SaveCopyAs(Workbook, string, bool, bool) - 另存副本
- SaveAs(Workbook, string) - 另存为简化版
- SaveCopyAs(Workbook, string) - 另存副本简化版

#### ETExcelExtensions_Format - 格式设置
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Format.cs
**功能**: 提供Excel单元格格式化和样式设置功能
**主要类**: ETExcelExtensions_Format (静态扩展类)
**核心方法**:
- GetCellFontWidth(Range) - 获取单元格字体宽度
- Formate设置背景色(Range, EnumColorNum) - 设置背景色
- Format设置边框(Range, XlLineStyle, EnumColorNum) - 设置边框
- Format设置边框(Range) - 设置默认边框
- Format设置边框(Range, XlLineStyle, XlLineStyle, EnumColorNum) - 设置详细边框
- Format条件格式警示色(Range, EnumWarningColor, int, bool) - 设置条件格式警示色
- Format条件格式警示色(IEnumerable<Range>, EnumWarningColor, int, bool) - 批量设置条件格式
- Format设置表格外圈双线(Range) - 设置表格外圈双线
- Format设置警示色(Range, EnumWarningColor, bool) - 设置警示色
- ParseWarningColor(string, bool) - 解析警示色

#### ETExcelExtensions_GetObjects - 对象获取
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_GetObjects.cs
**功能**: 提供Excel对象获取和范围操作的核心功能
**主要类**: ETExcelExtensions_GetObjects (静态扩展类)
**核心方法**:
- GetWorkbookNameByFormula(string) - 从公式获取工作簿名
- GetWorkbookNameByFormulaThenByName(string) - 从公式获取工作簿对象
- GetWorkbookByName(string) - 按名称获取工作簿
- GetParent(Worksheet) - 获取工作表父工作簿
- GetParent(Range) - 获取范围父工作表
- GetWorksheetNameByFormula(string) - 从公式获取工作表名
- GetWorksheetByFormulaThenByName(string) - 从公式获取工作表对象
- GetWorksheetByName(Workbook, string) - 按名称获取工作表
- GetWorksheetByAlias(Workbook, string) - 按别名获取工作表
- GetWorksheetAlias(Worksheet) - 获取工作表别名
- AddWorksheetAlias(Worksheet, string) - 添加工作表别名
- GetRangeByFormulaThenByValue(Range) - 从公式或值获取范围
- GetRangeByValue(Range) - 从值获取范围
- GetRangeByFormula(Range) - 从公式获取范围
- GetRangeByFormulaParser(string) - 解析公式获取范围
- GetRangeByRowColumnCoordinate(string) - 从行列坐标获取范围
- GetCellInTargetColumnByLookupRange(Range, Range) - 在目标列查找单元格
- GetCellInTargetColumnByRow(Range, int) - 按行号在目标列查找单元格
- GetColumnsInRange(Range, int, int) - 获取范围中的列
- GetRangeValueAsString(Range) - 获取范围值字符串
- GetValue(Range) - 获取范围值
- TableAddressAlign(Range) - 表格地址对齐
- GetVisibleRange(Range) - 获取可见范围
- UnionRanges(IEnumerable<Range>) - 合并范围
- Resize(Range) - 调整范围大小
- OptimizeRangeSize(Range) - 优化范围大小
- GetSelectionRange(bool, bool) - 获取选择范围
- GetSelectionColumn(int) - 获取选择列
- GetSelectionRow(int) - 获取选择行
- GetColumn(Range, int) - 获取指定列
- GetRow(Range, int) - 获取指定行
- GetRangeUnderFilter(Range) - 获取筛选下的范围
- GetRangeAfterCell2(Range, Range, EnumRowColumn) - 获取单元格后的范围2
- GetRangeAfterCell(Range, Range, EnumRowColumn) - 获取单元格后的范围
- GetRangeFormula(Range) - 获取范围公式
- GetVisibleRowCount(Range) - 获取可见行数
- GetCellValueAsFirstNonEmptyCellAbove(Range) - 获取上方首个非空单元格
- GetCellValueAsFirstNonEmptyCellLeft(Range) - 获取左侧首个非空单元格
- GetValueAddressListDic(Range, out List<string>, bool) - 获取值地址列表字典
- GetValueAddressListDic(Range, bool) - 获取值地址列表字典简化版

#### ETExcelExtensions_Conversion - 转换操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Conversion.cs
**功能**: 提供Excel数据转换、验证和格式化功能
**主要类**: ETExcelExtensions_Conversion (静态扩展类)
**核心方法**:
- ConvertToNumeric(Range, bool) - 转换为数值
- ConvertRangeToStringSet(Range) - 转换范围为字符串集合
- ConvertRangeToStringList(Range, bool, bool) - 转换范围为字符串列表
- RowsNumberToArray(Range) - 行号转数组
- ColumnsNumberToArray(Range) - 列号转数组
- GetVisibleCellsValueAsArray(Range) - 获取可见单元格值数组
- GetVisibleCellsAsArray(Range) - 获取可见单元格数组
- GetVisibleCellsIndices(Range) - 获取可见单元格索引
- FillResultArray(object[,], List<int>, List<int>, Range, bool) - 填充结果数组
- ExtractColumnData(object[,], int) - 提取列数据
- ConvertRangeToCSV(Range, string, bool, bool) - 转换范围为CSV
- GetFullAddress(Range) - 获取完整地址
- GetCodeName(Range) - 获取代码名称
- GetRangeText(object) - 获取范围文本
- IsWorkbookExists(string) - 检查工作簿存在
- IsWorksheetExists(Workbook, string) - 检查工作表存在
- IsFormulaString(string) - 检查公式字符串
- IsCellEmpty(Range, bool) - 检查单元格空
- IsCellEmptyOrWhiteSpace(Range) - 检查单元格空白
- IsCellFormula(Range) - 检查单元格公式
- IsCellNonText(Range) - 检查单元格非文本
- IsCellText(Range) - 检查单元格文本
- IsCellLogical(Range) - 检查单元格逻辑值
- IsCellNumber(Range) - 检查单元格数值
- IsCellError(Range) - 检查单元格错误
- CalculateSum(Range) - 计算范围求和

#### ETExcelExtensions_StringProcessing - 字符串处理
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_StringProcessing.cs
**功能**: 提供Excel字符串处理和转换功能
**主要类**: ETExcelExtensions_StringProcessing (静态扩展类)
**核心方法**:
- ConvertRangeToHalfWidth(Range, bool) - 转换范围为半角字符
- ConvertStringToHalfWidth(string, string) - 转换字符串为半角
- ConvertNumberToColumnName(int) - 数字转列名
- ParseVisibilityState(string) - 解析可见性状态

#### ETExcelExtensions_TextSearch - 文本搜索
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_TextSearch.cs
**功能**: 提供Excel文本搜索和查找功能
**主要类**: ETExcelExtensions_TextSearch (静态扩展类)
**核心方法**:
- FindFirstCell(Range, string) - 查找首个单元格
- FindFirstCellByRegex(Range, string) - 正则查找首个单元格
- FuzzySearch(Range, string) - 模糊搜索
- MultiStringSearch(Range, string[], bool, bool) - 多字符串搜索
- FuzzySearch(Range, string, EnumRowColumn, bool, bool) - 模糊搜索扩展
- FindExcludedText(Range, List<string>, bool) - 查找排除文本
- FindCellsExcludingText(Range, string, bool) - 查找排除文本单元格
- FindNonEmptyCells(Range, out Dictionary<int, bool>, bool) - 查找非空单元格

#### ETExcelExtensions_FilterAndHeader - 筛选和标题
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_FilterAndHeader.cs
**功能**: 提供Excel筛选和标题行操作功能
**主要类**: ETExcelExtensions_FilterAndHeader (静态扩展类)
**核心方法**:
- SetActiveSheetFilterRow(int) - 设置活动表筛选行
- SetWorksheetFilterRow(Worksheet, int) - 设置工作表筛选行
- FindColumnsByHeaderTitles(string, Range, Action<string>) - 按标题查找列
- FindColumnsByHeaderTitlesInCells(string, Range, Action<string>) - 在单元格中按标题查找列
- FindColumnByHeaderTitle(string, Range, Action<string>) - 按标题查找单列
- GetWorksheetFilterRowNumber(Worksheet) - 获取工作表筛选行号
- GetAutoFilterRowNumber() - 获取自动筛选行号
- GetAutoFilterRowNumber(Range) - 获取范围自动筛选行号
- GetAutoFilterRow(Worksheet) - 获取自动筛选行
- GetAutoFilterRow(Range) - 获取范围自动筛选行
- GetAutoFilterRow() - 获取自动筛选行
- GetRangeBelowFilterRow(Range) - 获取筛选行下方范围
- CreateColumnMapping(Range, Range) - 创建列映射

#### ETExcelExtensions_Validation - 数据验证
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Validation.cs
**功能**: 提供Excel数据验证和输入模式设置功能
**主要类**: ETExcelExtensions_Validation (静态扩展类)
**核心方法**:
- RangeIsContainValidation(Range) - 检查范围包含验证
- GetValidationOptions(Range) - 获取验证选项
- GetValidationOptions(string) - 从公式获取验证选项
- DelValidation(Range) - 删除验证
- GetValidation(Range) - 获取验证规则
- SetCellInputMode(Range, string) - 设置单元格输入模式
- GetCellInputMode(Range) - 获取单元格输入模式

#### ETExcelExtensions_常用操作 - 常用操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_常用操作.cs
**功能**: 提供Excel常用格式化和条件格式操作
**主要类**: ETExcelExtensions_常用操作 (静态扩展类)
**核心方法**:
- Format标记重复值(Range) - 标记重复值
- RemoveAllConditionalFormats移除条件格式(Range) - 移除条件格式

#### ETExcelExtensions_工作表操作 - 工作表操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_工作表操作.cs
**功能**: 提供Excel工作表显示隐藏、删除和页眉页脚设置
**主要类**: ETExcelExtensions_工作表操作 (静态扩展类)
**核心方法**:
- Set显示隐藏表(Worksheet, string) - 设置工作表显示隐藏
- Delete删除工作表(Worksheet, bool) - 删除工作表
- Set设置页眉(Worksheet, string, string, string) - 设置页眉
- Set设置页脚(Worksheet, string, string, string) - 设置页脚

#### ETExcelExtensions_工作簿操作 - 工作簿操作
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_工作簿操作.cs
**功能**: 提供Excel工作簿打开和工作表复制功能
**主要类**: ETExcelExtensions_工作簿操作 (静态扩展类)
**核心方法**:
- Open工作簿(string, bool) - 打开工作簿
- Copy复制工作表(Worksheet, Workbook) - 复制工作表

#### ETExcelExtensions_单元格填写 - 单元格填写
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_单元格填写.cs
**功能**: 提供Excel单元格填充、赋值和数据插入功能
**主要类**: ETExcelExtensions_单元格填写 (静态扩展类)
**核心方法**:
- Fill填充空白单元格(Range, object, bool) - 填充空白单元格
- Fast快速赋值(Range, object, bool) - 快速赋值
- Replace替换字符(Range, string, string, bool) - 替换字符
- Del保留首单元格式其他删除(Range, bool) - 保留首单元格式其他删除
- Clear清除内容(Range) - 清除内容
- InsertRows插入行(Range, int, bool) - 插入行
- InsertDataBasedOnHeader插入数据(List<dynamic>, Range, Range) - 基于标题插入数据

---

## 📊 批次1完成情况

**已完成**: 批次1 - 核心工具类模块 (18个文件)
**完成时间**: 2025-01-13
**文件数量**: 18个核心工具类文件
**方法总数**: 约300+个public方法

#### ETExcelAutoUpdate - Excel自动更新
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelAutoUpdate.cs
**功能**: Excel数据自动更新策略执行
**主要类**: ETExcelAutoUpdate
**核心方法**:
- ExecuteUpdate(Worksheet, string) - 执行更新策略
- ExecuteUpdate(IEnumerable<UpdateStrategy>) - 批量执行更新

#### ETExcelAutoUpdate2 - Excel自动更新2
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelAutoUpdate2.cs
**功能**: Excel数据自动更新增强版
**主要类**: ETExcelAutoUpdate2
**核心方法**:
- WriteLog(string) - 写入日志
- UpdataValidation(Worksheet) - 更新验证
- FindColumnsByHeaders(string, Range) - 按标题查找列
- FindColumnsByHeaders(string, Range[]) - 按标题查找列
- FindColumnsByHeaders(string[], Range[]) - 按标题查找列
- ExecuteRangeUpdate(string, ...) - 执行范围更新
- SetSourceHeaders(Array) - 设置源标题
- SetTargetHeaders(Array) - 设置目标标题

#### ETExcelConfig - Excel配置管理
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelConfig.cs
**功能**: Excel模板和配置管理
**主要类**: ETExcelConfig (静态类)
**核心方法**:
- TemplateExcelPath - 模板Excel路径
- CopyTemplateSheetFromExcelDnaData(string, Workbook) - 复制模板工作表
- OpenTemplateWorkbookInBackground(bool) - 后台打开模板工作簿
- CloseTemplateWorkbook() - 关闭模板工作簿
- LoadDataFromTemplateWorkbook(string) - 从模板加载数据

#### ETExcelDataSummarizer - Excel数据汇总
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelDataSummarizer.cs
**功能**: Excel数据汇总和统计
**主要类**: ETExcelDataSummarizer (静态类)
**核心方法**:
- SummarizeData(Range, ...) - 汇总数据

#### ETExcelExtensions_CellComments - 单元格注释扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_CellComments.cs
**功能**: Excel单元格注释操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- SetComment(Range, string, bool) - 设置注释
- DelComment(Range) - 删除注释
- AddOrUpdateComment(Range, string) - 添加或更新注释

#### ETExcelExtensions_DisplayNamesShapes - 显示名称和形状扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_DisplayNamesShapes.cs
**功能**: Excel命名区域和形状显示操作
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- ShowAllNamedRanges(Worksheet) - 显示所有命名区域
- DeleteAllShapesInWorksheet(Worksheet) - 删除工作表所有形状

#### ETExcelExtensions_HiddenComments - 隐藏注释扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_HiddenComments.cs
**功能**: Excel隐藏注释操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- SetCellHiddenComment(Range, string) - 设置单元格隐藏注释
- GetCellHiddenComment(Range) - 获取单元格隐藏注释
- SetColumnHiddenComment(Range, string) - 设置列隐藏注释
- GetColumnHiddenComment(Range) - 获取列隐藏注释

#### ETExcelExtensions_Initialize - 初始化扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Initialize.cs
**功能**: Excel初始化和查找操作
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- FindLatLongColumns(Worksheet, int) - 查找经纬度列

#### ETExcelExtensions_InputDialog - 输入对话框扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_InputDialog.cs
**功能**: Excel输入对话框操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- GetInputAndSetCell(string, Range) - 获取输入并设置单元格
- GetDoubleInput(string) - 获取双精度输入
- VerifyCode(string) - 验证码验证
- ShowConfirmDialog(string) - 显示确认对话框
- GetRangeSelection(string, bool) - 获取范围选择
- GetTextInput(string, string) - 获取文本输入
- GetNumberInput(string, string) - 获取数值输入

#### ETExcelExtensions_Jump - 跳转扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Jump.cs
**功能**: Excel跳转和激活操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- ActivateWorkbook(Workbook) - 激活工作簿
- ActivateWorksheet(Worksheet, bool) - 激活工作表
- JumpToRange(Range) - 跳转到范围
- JumpToAddress(string) - 跳转到地址

#### ETExcelExtensions_Other - 其他扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Other.cs
**功能**: Excel其他杂项操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- PlayNotificationSound() - 播放通知声音
- ExportToPdf(Workbook, string) - 导出为PDF
- ExportToPdf(Workbook, ...) - 导出为PDF(扩展)

#### ETExcelExtensions_RowColumnNumbers - 行列号扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_RowColumnNumbers.cs
**功能**: Excel行列号获取操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- GetWorksheetMaxRow(Worksheet) - 获取工作表最大行
- GetRangeMaxRow(Range) - 获取范围最大行
- GetMaxColumnNumber(Worksheet) - 获取最大列号
- GetRangeMaxColumn(Range) - 获取范围最大列
- GetWorksheetMinRow(Worksheet) - 获取工作表最小行
- GetRangeMinRow(Range) - 获取范围最小行
- GetWorksheetMinColumn(Worksheet) - 获取工作表最小列

#### ETExcelExtensions_Struct - 结构定义
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_Struct.cs
**功能**: Excel扩展相关结构定义
**主要类**: 结构定义文件
**核心方法**:
- (无public方法，仅包含结构定义)

#### ETExcelExtensions_TagOperations - 标签操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_TagOperations.cs
**功能**: Excel标签操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- FindCellsByTagCondition(Range, ...) - 按标签条件查找单元格
- ExtractTagsFromRange(Range, ...) - 从范围提取标签
- ExtractAllTags(Range, ...) - 提取所有标签

#### ETExcelExtensions_WPS - WPS兼容扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_WPS.cs
**功能**: WPS Office兼容性操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- IsWPS() - 检查是否为WPS
- OpenFileByWps(string) - 用WPS打开文件
- OpenFileByDefaultOffice(string) - 用默认Office打开文件
- AddVSTORegistryToWPS() - 添加VSTO注册表到WPS

#### ETExcelExtensions_WorksheetProperties - 工作表属性扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_WorksheetProperties.cs
**功能**: Excel工作表自定义属性操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- SetCustomizeAttributes(Worksheet, ...) - 设置自定义属性
- GetCustomizeAttributes(Worksheet, string) - 获取自定义属性
- DeleteCustomizeAttribute(Worksheet, string) - 删除自定义属性

#### ETExcelExtensions_WorksheetProtection - 工作表保护扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_WorksheetProtection.cs
**功能**: Excel工作表保护和锁定操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- IsLock(Worksheet) - 检查是否锁定
- IsCanSelect(Worksheet) - 检查是否可选择
- Lock(Range) - 锁定范围
- Lock(Worksheet, string) - 锁定工作表
- UnLockRange(Range) - 解锁范围
- UnLockWorksheet(Worksheet, string) - 解锁工作表

#### ETExcelExtensions_填写辅助操作 - 填写辅助操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_填写辅助操作.cs
**功能**: Excel填写辅助和下拉选项操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- SetOptional设置下拉可选项(Range, ...) - 设置下拉可选项
- SetOptional设置下拉可选项2(Range, ...) - 设置下拉可选项2
- SetOptional设置下拉可选项(Range, ...) - 设置下拉可选项(重载)
- SetOptional(Range, Range, string) - 设置可选项
- TurnDropDownToFixedString(Range) - 转换下拉为固定字符串
- SetCellToCandidate1(Range) - 设置单元格为候选1

#### ETExcelExtensions_外部连接操作 - 外部连接操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_外部连接操作.cs
**功能**: Excel外部连接管理操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Delete外部连接(Workbook) - 删除外部连接

#### ETExcelExtensions_字符处理操作 - 字符处理操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_字符处理操作.cs
**功能**: Excel字符处理操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Trim去前后空格(Range, bool) - 去除前后空格

#### ETExcelExtensions_排序操作 - 排序操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_排序操作.cs
**功能**: Excel排序操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- SortColumn(Range, XlSortOrder) - 列排序

#### ETExcelExtensions_格式操作 - 格式操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_格式操作.cs
**功能**: Excel格式设置操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Del文本格式(Range) - 删除文本格式
- Del删除条件格式(Range) - 删除条件格式
- Set数字格式刷(Range, Range) - 设置数字格式刷
- Set设置字体大小(Range, string) - 设置字体大小
- Set字体格式刷(Range, Range) - 设置字体格式刷
- Set背景格式刷(Range, Range) - 设置背景格式刷
- Set设置字型(Range, string) - 设置字型
- Set设置格式(Range, string) - 设置格式
- Set通用格式(Range) - 设置通用格式
- Set文本格式(Range) - 设置文本格式

#### ETExcelExtensions_筛选操作 - 筛选操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_筛选操作.cs
**功能**: Excel筛选操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Filter撤销所有筛选条件(Range) - 撤销所有筛选条件
- Filter撤销所有筛选条件(Worksheet) - 撤销所有筛选条件
- Filter取消设置指定列筛选(Range) - 取消设置指定列筛选
- Filter取消筛选模式(Worksheet) - 取消筛选模式
- Filter设置条件筛选(Range, ...) - 设置条件筛选(多重载)
- Filter设置排除条件筛选(Range, ...) - 设置排除条件筛选(多重载)
- Filter标色并筛选行(IEnumerable<Range>, ...) - 标色并筛选行
- Filter标色并筛选行(Range, ...) - 标色并筛选行

#### ETExcelExtensions_表格操作 - 表格操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_表格操作.cs
**功能**: Excel表格操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Del删除隐藏行(Range, bool) - 删除隐藏行
- Show显示隐藏行列(Range, string) - 显示隐藏行列
- Set设置行高(Range, string) - 设置行高
- Set倍数行高(Range, string) - 设置倍数行高
- Set自动行高(Range) - 设置自动行高
- Set设置列宽(Range, string) - 设置列宽
- Set设置排序(Range, string) - 设置排序
- Set控制列层级(Worksheet, string) - 控制列层级
- Set显示层级按键(Worksheet, string) - 显示层级按键

#### ETExcelExtensions_转换操作 - 转换操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_转换操作.cs
**功能**: Excel转换操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Convert金额转大写(Range, bool) - 金额转大写

#### ETExcelExtensions_页面设置操作 - 页面设置操作扩展
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/ETExcelExtensions_页面设置操作.cs
**功能**: Excel页面设置操作扩展
**主要类**: ETExcelExtensions (静态扩展类)
**核心方法**:
- Hide隐藏范围外内容(Range) - 隐藏范围外内容

#### HHFormManager - 窗体管理器
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/FormManager/HHFormManager.cs
**功能**: Excel窗体管理器
**主要类**: HHFormManager
**核心方法**:
- FindOpenForm(string) - 查找打开的窗体
- OpenForm(Form, XlFormPosition, bool) - 打开窗体
- NotifyFormsSelectionChange(Range, string) - 通知窗体选择变化

#### HHUcDirectorySelect - 目录选择控件
**路径**: ExtensionsTools/ExtensionsTools/ETExcel/HHUcDirectorySelect.cs
**功能**: 目录选择用户控件
**主要类**: HHUcDirectorySelect
**核心方法**:
- PathSelectedHandler(string) - 路径选择处理委托
- HHUcDirectorySelect() - 构造函数

---

## 📊 批次2子批次2-1完成情况

**已完成**: 批次2子批次2-1 - Excel操作模块核心文件 (16个文件)
**完成时间**: 2025-01-13
**文件数量**: 16个Excel扩展文件
**方法总数**: 约150+个public方法

## 📊 批次2子批次2-2完成情况

**已完成**: 批次2子批次2-2 - Excel操作模块扩展文件 (27个文件)
**完成时间**: 2025-01-13
**文件数量**: 27个Excel扩展文件
**方法总数**: 约100+个public方法
**批次2总完成**: 43个Excel文件全部完成

---

## 🔐 授权管理模块

#### ETLicenseManager - 授权管理器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseManager.cs
**功能**: 提供授权管理的核心功能和配置管理
**主要类**: ETLicenseManager (静态类), ETBaseConfig
**核心方法**:
- GetBaseInfoPath() - 获取基础信息路径
- SetBaseInfoPath(string) - 设置基础信息路径
- LaunchLicenseGenerator() - 启动授权生成器
- LaunchLicenseGenerator(string) - 启动授权生成器
- CreateLicenseControllerFromFile(string, string, string) - 从文件创建控制器
- CreateCombinedLicenseController(string, string, string, string, int) - 创建组合控制器
- LoadBaseConfig(string, string) - 加载基础配置
- SaveBaseConfig(ETBaseConfig, string, string) - 保存基础配置

#### ETLicenseController - 授权控制器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseController.cs
**功能**: 授权验证和权限控制的核心控制器
**主要类**: ETLicenseController
**核心方法**:
- ETLicenseController(IETLicenseProvider, IETMachineCodeProvider, string, string[]) - 构造函数
- SetCurrentUser(string, string[]) - 设置当前用户
- GetCurrentMachineCode() - 获取机器码
- RefreshLicenseInfoAsync(bool) - 刷新授权信息
- ForceRefreshAllAsync() - 强制刷新全部
- ForceRefreshAllWithCallbackAsync() - 强制刷新带回调
- HasPermissionAsync(string) - 检查权限
- CheckPermissionWithExpireTimeAsync(string) - 检查权限含过期时间
- GetAllPermissionsAsync() - 获取所有权限
- Dispose() - 释放资源

#### ETLicenseProvider - 授权提供器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseProvider.cs
**功能**: 提供多种授权信息获取方式(远程/文件/SMB/组合)
**主要类**: ETRemoteLicenseProvider, ETFileLicenseProvider, ETSmbLicenseProvider, ETCombinedLicenseProvider
**核心方法**:
- ETRemoteLicenseProvider(string, IETLicenseCryptoService, string, int) - 远程提供器构造
- GetLicenseInfoAsync() - 获取授权信息
- ETFileLicenseProvider(string, IETLicenseCryptoService) - 文件提供器构造
- ETSmbLicenseProvider(string, IETLicenseCryptoService, int) - SMB提供器构造
- ETCombinedLicenseProvider(string, IETLicenseCryptoService, string, string, int) - 组合提供器构造
- ForceRefreshAsync() - 强制刷新
- ForceRefreshWithCallbackAsync() - 强制刷新带回调
- Dispose() - 释放资源

#### ETLicenseGenerator - 授权生成器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseGenerator.cs
**功能**: 生成和管理授权文件，支持OSS存储
**主要类**: ETLicenseGenerator, DateTimeConverter
**核心方法**:
- ETLicenseGenerator(IETLicenseCryptoService) - 构造函数
- ETLicenseGenerator(IETLicenseCryptoService, IETOssService) - 构造函数含OSS
- GenerateLicenseFileAsync(ETLicenseInfo, string) - 生成授权文件
- LoadFromOssAsync(string) - 从OSS加载
- SaveToOssAsync(ETLicenseInfo, string) - 保存到OSS
- CreateSampleLicenseInfo() - 创建示例授权信息
- Read(Utf8JsonReader, Type, JsonSerializerOptions) - JSON读取
- Write(Utf8JsonWriter, DateTime, JsonSerializerOptions) - JSON写入

#### ETLicenseVerifier - 授权验证器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseVerifier.cs
**功能**: 验证授权文件的有效性
**主要类**: ETLicenseVerifier
**核心方法**:
- VerifyLicense(string) - 验证授权

#### ETPermissionManager - 权限管理器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETPermissionManager.cs
**功能**: 管理权限检查和UI权限映射
**主要类**: ETPermissionManager
**核心方法**:
- ETPermissionManager(ETLicenseController, string[], int) - 构造函数
- RegisterPermissionUIMapping(string, List<Action<bool>>) - 注册权限UI映射
- RegisterPermissionUIMappings(Dictionary<string, List<Action<bool>>>) - 批量注册映射
- InitializePermissions(Action<bool>) - 初始化权限
- ApplyPermissionsToUI() - 应用权限到UI
- CheckPermissionWithExpireTime(string) - 检查权限含过期时间
- HasPermission(string) - 检查权限
- ForceRefreshPermissionsAndUI() - 强制刷新权限和UI
- ForceRefreshPermissionsAndUIAsync() - 异步强制刷新
- ClearPermissionCache() - 清除权限缓存
- GetCachedPermissionCount() - 获取缓存权限数量
- GetRequiredPermissions() - 获取必需权限

#### ETOssService - OSS存储服务
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETOssService.cs
**功能**: 提供阿里云OSS存储服务，用于授权文件云端存储
**主要类**: ETOssService
**核心方法**:
- ETOssService(ETOssConfig, IETLicenseCryptoService) - 构造函数
- GetLicenseFromOssAsync(string) - 从OSS获取授权
- SaveLicenseToOssAsync(ETLicenseInfo, string) - 保存授权到OSS

#### ETLocationService - 位置服务
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLocationService.cs
**功能**: 提供地理位置检测和地域限制验证功能
**主要类**: ETIp9LocationService
**核心方法**:
- ETIp9LocationService(string) - 构造函数
- GetCurrentLocationAsync() - 获取当前位置
- CheckLocationRestrictionAsync(string) - 检查地域限制
- Dispose() - 释放资源

#### ETMachineCodeProvider - 机器码提供器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETMachineCodeProvider.cs
**功能**: 生成唯一的机器标识码用于授权绑定
**主要类**: ETMachineCodeProvider
**核心方法**:
- GetMachineCode() - 获取机器码

#### ETLicenseCryptoService - 授权加密服务
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseCryptoService.cs
**功能**: 提供授权文件的加密解密和压缩功能
**主要类**: ETAesLicenseCryptoService
**核心方法**:
- ETAesLicenseCryptoService(string) - 构造函数
- EncryptAndCompress(string) - 加密并压缩
- DecryptAndDecompress(byte[]) - 解密并解压

#### ETControlPermissionManager - 控件权限管理器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETControlPermissionManager.cs
**功能**: 管理控件级别的权限控制和标题混淆
**主要类**: ETControlPermissionManager, ETControlInfo, ETControlPermissionConfig
**核心方法**:
- ETControlPermissionManager(ETPermissionManager, ETControlPermissionConfig) - 构造函数
- RegisterControlPermissionMapping(string, string, string) - 注册控件权限映射
- RegisterControlPermissionMappings(Dictionary<string, (string, string)>) - 批量注册映射
- HasControlPermission(string) - 检查控件权限
- GenerateObfuscatedTitle(string, int?) - 生成混淆标题
- GetControlDisplayTitle(string) - 获取控件显示标题
- RefreshAllControlPermissions() - 刷新所有控件权限
- ClearPermissionCache() - 清除权限缓存
- GetCacheStatistics() - 获取缓存统计
- GetControlInfo(string) - 获取控件信息
- GetAllControlInfos() - 获取所有控件信息
- GetControlsByPermission(string) - 按权限获取控件
- IsControlRegistered(string) - 检查控件是否注册
- GetControlDisplayTitles(IEnumerable<string>) - 批量获取显示标题
- CheckControlPermissions(IEnumerable<string>) - 批量检查权限
- GetConfig() - 获取配置
- UpdateObfuscationConfig(bool?, int?, ObfuscationSeedStrategy?) - 更新混淆配置
- GetDebugInfo() - 获取调试信息
- ValidateConfig() - 验证配置
- UpdateControlTitle(object, string) - 更新控件标题
- BatchUpdateControlTitles(Dictionary<string, object>) - 批量更新标题
- RefreshAllControlTitlesInContainer(object, Func<string, bool>) - 刷新容器内标题
- BatchCheckControlPermissions(IEnumerable<string>) - 批量检查权限
- BatchGetControlDisplayTitles(IEnumerable<string>) - 批量获取标题

#### ETUIPermissionManager - UI权限管理器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETUIPermissionManager.cs
**功能**: 提供UI权限管理的基础抽象类和通用功能
**主要类**: ETUIPermissionManager
**核心方法**:
- Initialize() - 初始化
- ApplyPermissionsToUI() - 应用权限到UI
- ForceRefreshPermissionsAndUI() - 强制刷新权限和UI
- HasPermission(string) - 检查权限
- CheckPermissionWithExpireTime(string) - 检查权限含过期时间
- ClearPermissionCache() - 清除权限缓存
- GetRequiredPermissions() - 获取必需权限
- RefreshControlTitles() - 刷新控件标题
- HasControlPermission(string) - 检查控件权限
- GetControlPermissionManager() - 获取控件权限管理器
- GetControlDisplayTitles(IEnumerable<string>) - 获取控件显示标题
- CheckControlPermissions(IEnumerable<string>) - 检查控件权限
- RefreshRibbonControlTitles() - 刷新Ribbon控件标题
- GetControlDisplayTitle(string) - 获取控件显示标题
- UpdateControlTitle(object, string) - 更新控件标题
- BatchUpdateControlTitles(Dictionary<string, object>) - 批量更新控件标题

#### ETAboutLicenseForm - 关于授权窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETAboutLicenseForm.cs
**功能**: 显示授权信息和机器码的关于窗体
**主要类**: ETAboutLicenseForm
**核心方法**:
- ETAboutLicenseForm() - 构造函数

#### ETControlMappingGenerator - 控件映射生成器
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETControlMappingGenerator.cs
**功能**: 生成Ribbon控件的映射关系和权限配置
**主要类**: ETControlMappingGenerator, ETRibbonControlInfo
**核心方法**:
- ETControlMappingGenerator(object, Type, BindingFlags) - 构造函数
- GetControlStructure(Func<Type, bool>) - 获取控件结构
- GenerateControlTitleMapping(Func<Type, bool>) - 生成标题映射
- GenerateControlPermissionMapping(Dictionary<string, List<string>>, Func<Type, bool>) - 生成权限映射
- CreateForRibbon(object, Type) - 创建Ribbon映射器
- IsRibbonControl(Type) - 判断Ribbon控件

#### ETControlMappingManagerBase - 控件映射管理基类
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETControlMappingManagerBase.cs
**功能**: 提供控件映射管理的基础抽象功能
**主要类**: ETControlMappingManagerBase
**核心方法**:
- EnsureGlobalMappingsInitialized() - 确保映射初始化
- ForceInitializeGlobalMappings() - 强制初始化映射
- GetGlobalControlTitleMapping() - 获取全局标题映射
- GetGlobalControlPermissionMapping() - 获取全局权限映射

#### ETLicenseAdminLoginForm - 授权管理员登录窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseAdminLoginForm.cs
**功能**: 提供授权管理员身份验证的登录界面
**主要类**: ETLicenseAdminLoginForm
**核心方法**:
- ETLicenseAdminLoginForm() - 构造函数

#### ETLicenseEntityForm - 授权实体编辑窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseEntityForm.cs
**功能**: 编辑用户和用户组授权实体信息
**主要类**: ETLicenseEntityForm
**核心方法**:
- ETLicenseEntityForm() - 构造函数
- ETLicenseEntityForm(string, string, string, string) - 编辑模式构造
- EntityId - 获取实体ID
- MachineCode - 获取机器码
- Groups - 获取用户组

#### ETLicenseGeneratorForm - 授权文件生成器窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLicenseGeneratorForm.cs
**功能**: 提供完整的授权文件生成和管理界面
**主要类**: ETLicenseGeneratorForm
**核心方法**:
- ETLicenseGeneratorForm() - 默认构造函数
- ETLicenseGeneratorForm(string) - 指定配置路径构造
- ETLicenseGeneratorForm(string, ETBaseConfig, string) - 完整参数构造
- OpenLicenseGeneratorForm(string) - 打开授权管理窗体

#### ETLocationTestForm - 地域限制测试窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETLocationTestForm.cs
**功能**: 测试地域限制功能的调试界面
**主要类**: ETLocationTestForm
**核心方法**:
- ETLocationTestForm() - 构造函数

#### ETOssBrowserForm - OSS浏览器窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETOssBrowserForm.cs
**功能**: 提供OSS存储的文件浏览和管理界面
**主要类**: ETOssBrowserForm, TextInputDialog
**核心方法**:
- ETOssBrowserForm(ETOssConfig, IETLicenseCryptoService) - 构造函数
- TextInputDialog(string, string, string) - 文本输入对话框构造

#### ETOssConfig - OSS配置
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETOssConfig.cs
**功能**: OSS存储服务的配置信息管理
**主要类**: ETOssConfig
**核心方法**:
- CreateDefault() - 创建默认配置

#### ETOssConfigForm - OSS配置窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETOssConfigForm.cs
**功能**: 编辑OSS存储配置的界面窗体
**主要类**: ETOssConfigForm
**核心方法**:
- ETOssConfigForm() - 默认构造函数
- ETOssConfigForm(ETOssConfig) - 指定配置构造

#### ETPermissionNamesForm - 权限名称编辑窗体
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETPermissionNamesForm.cs
**功能**: 编辑和管理权限名称列表的界面
**主要类**: ETPermissionNamesForm
**核心方法**:
- ETPermissionNamesForm() - 默认构造函数
- ETPermissionNamesForm(List<string>) - 指定权限列表构造

#### ETThreadSafeCallbackHelper - 线程安全回调助手
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/ETThreadSafeCallbackHelper.cs
**功能**: 提供线程安全的回调函数调用机制
**主要类**: ETThreadSafeCallbackHelper
**核心方法**:
- SafeInvokeNetworkLicenseUpdated(NetworkLicenseUpdatedEventHandler, ETLicenseInfo, string) - 安全调用网络授权更新
- TestCallbackMechanism(string) - 测试回调机制
- SafeInvokeAction(Action, int) - 安全调用Action
- SafeInvokeAction<T>(Action<T>, T, int) - 安全调用泛型Action
- SafeInvokeAction<T1, T2>(Action<T1, T2>, T1, T2, int) - 安全调用双参数Action

#### ETDictionaryExtensions - 字典扩展
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/Extensions/ETDictionaryExtensions.cs
**功能**: 为Dictionary提供扩展方法
**主要类**: ETDictionaryExtensions (静态扩展类)
**核心方法**:
- GetValueOrDefault<TKey, TValue>(Dictionary<TKey, TValue>, TKey, TValue) - 获取值或默认值

#### InputBox - 输入对话框
**路径**: ExtensionsTools/ExtensionsTools/ETLicense/InputBox.cs
**功能**: 提供简单的文本输入对话框
**主要类**: InputBox
**核心方法**:
- InputBox(string, string, string) - 构造函数

## 📊 批次3子批次3-2完成情况

**已完成**: 批次3子批次3-2 - 授权管理模块UI和扩展文件 (15个文件)
**完成时间**: 2025-01-13
**文件数量**: 15个UI和扩展文件
**方法总数**: 约35个public方法
**总计**: 批次3全部完成 (27个文件，约125个public方法)

---

## 🏢 Office集成模块

### ETWord - Word文档操作
**路径**: ExtensionsTools/ExtensionsTools/ETWord/ETWord.cs
**功能**: Word文档批量处理和PDF转换功能
**主要类**: ETWord (静态类)
**核心方法**:
- ReplaceTextInWordPerRowsThread(string, Range, string, TextBox, TextBox) - 多线程替换文本
- ReplaceTextInWordPerRows(string, Range, string, TextBox, TextBox) - 批量替换文本
- WordToPdfPerRowsThread(Range, string, string, TextBox, TextBox) - 多线程转PDF
- WordToPdfPerRows(Range, string, string, TextBox, TextBox) - 批量转PDF
- WordToPdfToAnotherDirectory(Application, string, string, string) - 转PDF到目录
- WordToPdf(Application, string, string) - Word转PDF

### ETPowerPoint - PowerPoint操作
**路径**: ExtensionsTools/ExtensionsTools/ETPowerPoint/HHPowerPoint.cs
**功能**: PowerPoint文档批量处理和内容操作
**主要类**: HHPowerPoint (静态类)
**核心方法**:
- DeleteShapesOutsideSlides(IEnumerable<string>, string, string, TextBox) - 删除边界外形状
- InsertExcelRangeToPpt(Range, string, TextBox) - 插入Excel数据
- DeleteTextAndTable(Presentation, string, TextBox) - 删除文本表格
- ReplaceTextInPptPerRows(string, Range, string, TextBox, PptOperationMode, string) - 批量替换文本
- ReplaceTextInPresentation(Presentation, string, string, string, TextBox) - 替换演示文稿文本
- MoveTextAndTable(Presentation, string, TextBox) - 移动文本表格
- CopyTextAndTable(Presentation, Presentation, string, TextBox) - 复制文本表格

### ETVisio应用程序控制 - Visio应用管理
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_APP.cs
**功能**: Visio应用程序生命周期管理和控制
**主要类**: ETVisioHelper2013_APP (静态扩展类)
**核心方法**:
- IsVisioThreadRunning() - 检查Visio运行状态
- KillVisioApplication() - 强制终止Visio进程
- IsVisio2013OrLater(Application) - 检查Visio版本
- CreateApplication(bool) - 创建Visio应用实例
- QuitApplication(Application) - 关闭Visio应用
- ResponseDisable(Application) - 禁用窗口响应
- ResponseEnable(Application) - 启用窗口响应
- AlertEnable(Application) - 启用警告提示
- AlertDisable(Application) - 禁用警告提示
- SendVisioCommand(Application, VisUICmds) - 发送Visio命令
- FitPageToWindow(Application) - 适应页面到窗口

### ETVisio文档操作 - Visio文档管理
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Document.cs
**功能**: Visio文档打开、保存和关闭操作
**主要类**: ETVisioHelper2013_Document (静态扩展类)
**核心方法**:
- Open(Application, string, TextBox, bool) - 打开Visio文档
- FindOpenDocumentByPath(Application, string) - 查找已打开文档
- OpenWithoutVSS(Application, string) - 无模板打开文档
- Close(Document, bool) - 关闭Visio文档
- CloseAllVssFiles(Documents) - 关闭所有模板文件
- CloseAllVssFiles(Application) - 关闭应用模板文件
- Save(Document, bool) - 保存Visio文档
- SaveAs(Document, string) - 另存为文档
- SaveToAnotherDirectory(Document, string, bool) - 保存到其他目录
- SaveToAnotherDirectory(Document, string, string, bool) - 保存到指定目录

### ETVisio Excel集成 - Excel OLE操作
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Excel.cs
**功能**: Visio中Excel OLE对象的操作和转换
**主要类**: ETVisioHelper2013_Excel (静态类)
**核心方法**:
- GenerateExcelOLEShapes(Page) - 获取Excel OLE形状
- GenerateWorkbookFromExcelOLEShapes(Shape) - 从形状生成工作簿

### ETVisio其他工具 - Visio辅助功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Other.cs
**功能**: Visio操作的辅助工具和实用功能
**主要类**: ETVisioHelper2013_Other (静态类)
**核心方法**:
- GenerateTargetPath(string, string, string) - 生成目标路径
- IsShapeInFrameRange(RectangleRange, RectangleRange, double, double) - 检查形状范围
- ModifyShapeText(Page, List<(string, string, string, string)>) - 修改形状文本
- ShapeTextToExcel(Dictionary<string, List<TuxianSubShapeInfo>>, string, Range) - 形状文本到Excel

### ETVisio PDF转换 - PDF导出功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_PDF.cs
**功能**: Visio文档转换为PDF格式
**主要类**: ETVisioHelper2013_PDF (静态类)
**核心方法**:
- VisioToPdf(Application, string, string) - Visio转PDF
- VisioToPdfToAnotherDirectory(Application, string, string, string) - 转PDF到目录

### ETVisio页面操作 - 页面管理功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Page.cs
**功能**: Visio页面属性获取和操作管理
**主要类**: ETVisioHelper2013_Page (静态扩展类)
**核心方法**:
- FindPage(Document, string) - 查找指定页面
- IsBackgroundPage(Page) - 检查背景页面
- GetPageCellValue(Page, string) - 获取页面单元格值
- GetPageWidthInMillimeters(Page) - 获取页面宽度毫米
- GetPageHeightInMillimeters(Page) - 获取页面高度毫米
- GetPaperSizeInMillimeters(Page) - 获取纸张尺寸毫米
- GetPaperDrawingScale(Page) - 获取绘图比例
- FindBackgroundPage(Page) - 查找背景页面
- FindLastBackgroundPage(Page, int) - 查找最后背景页面
- GetBackgroundPaperSize(Page) - 获取背景纸张尺寸
- GetBackgroundPaperType(Page) - 获取背景纸张类型

### ETVisio形状操作 - 形状管理功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Shape.cs
**功能**: Visio形状的创建、操作和管理
**主要类**: ETVisioHelper2013_Shape (静态类)
**核心方法**:
- CalculateRotatedCorners(RectanglPosition) - 计算旋转角点
- FindShapesInNormalizedRegion(Page, RectangleRange) - 查找归一化区域形状
- IsShapeInNormalizedRegion(Shape, RectangleRange, double, double) - 检查形状在归一化区域
- FindShapesInRectangularRegion(Page, RectangleRange) - 查找矩形区域形状
- IsShapeInRectangularRegion(Shape, RectangleRange) - 检查形状在矩形区域
- GetShapeBoundingBox(Shape) - 获取形状边界框
- GetShapePositionInMillimeters(Shape) - 获取形状位置毫米
- ReadTextFromShapes(Page, Dictionary<string, RectangleRange>) - 读取形状文本
- SetShapePositionInMillimeters(Shape, RectanglPosition) - 设置形状位置毫米
- DeleteShapes(IEnumerable<Shape>) - 删除多个形状
- DeleteSingleShape(Shape) - 删除单个形状
- DeleteShapesInMultipleRegions(Page, List<RectangleRange>) - 删除多区域形状
- DeleteShapesOutsidePageBoundary(Page) - 删除页面边界外形状
- CopyShape(Page, Page, Shape) - 复制形状
- GroupShapes(List<Shape>) - 组合形状
- UngroupShapes(Shape) - 取消组合形状
- CreateRectangleText(Page, RectangleRange, string, double) - 创建矩形文本
- AlignShapes(Shape, Shape, AlignType) - 对齐形状
- AlignShapes(List<Shape>, Shape, AlignType) - 批量对齐形状
- ArrangeShapes(List<Shape>, AlignType) - 排列形状

### ETVisio形状扩展 - 形状位置转换
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Shape2.cs
**功能**: 形状位置坐标系统转换
**主要类**: ETVisioHelper2013_Shape2 (静态类)
**核心方法**:
- ConvertSubShapePosition(RectanglPosition, RectanglPosition, double, double) - 转换子形状位置
- ConvertSubShapePosition_BL(RectanglPosition, RectanglPosition, double, double) - 转换子形状位置BL

### ETVisio形状基础 - 形状属性操作
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_ShapeBase.cs
**功能**: 形状基础属性的获取和设置
**主要类**: ETVisioHelper2013_ShapeBase (静态扩展类)
**核心方法**:
- GetShapeCellValue(Shape, VisCellType) - 获取形状单元格值
- SetShapeCellValue(Shape, VisCellType, string) - 设置形状单元格值字符串
- SetShapeCellValue(Shape, VisCellType, double) - 设置形状单元格值数值
- GetShapeCellValue_MM(Shape, VisCellType) - 获取形状单元格值毫米
- SetShapeCellValue_MM(Shape, VisCellType, string) - 设置形状单元格值毫米字符串
- SetShapeCellValue_MM(Shape, VisCellType, double) - 设置形状单元格值毫米数值
- SetShapeText(Shape, object) - 设置形状文本
- GetShapeText(Shape) - 获取形状文本
- GetShapePinX(Shape) - 获取形状X坐标
- GetShapePinY(Shape) - 获取形状Y坐标
- GetShapeWidth(Shape) - 获取形状宽度
- GetShapeHeight(Shape) - 获取形状高度
- SetShapeWidth(Shape, double) - 设置形状宽度
- SetShapeHeight(Shape, double) - 设置形状高度
- SetShapePinX(Shape, double) - 设置形状X坐标
- SetShapePinY(Shape, double) - 设置形状Y坐标

### ETVisio形状搜索 - 形状查找功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Shape_Search.cs
**功能**: 基于正则表达式的形状搜索功能
**主要类**: ETVisioHelper2013_Shape_Search (静态类)
**核心方法**:
- FindShapesByRegex(Page, List<(string, string)>) - 正则搜索形状

### ETVisio结构定义 - 数据结构和常量
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Struct.cs
**功能**: Visio操作相关的数据结构和常量定义
**主要类**: ETVisioHelper2013_Struct, TuxianSubShapeInfo, RectangleRange, Size, RectanglPosition
**核心方法**:
- NF相对位置 (静态字典) - 南方标准相对位置
- SH相对位置 (静态字典) - 上海电网相对位置
- 图框比例值 (静态常量) - 图框标准比例
- A3横向/A3纵向/A4横向/A4纵向 (静态常量) - 图衔比例值
- TuxianSubShapeInfo() - 图衔子形状信息构造
- TuxianSubShapeInfo(string, string, string, string, string, string) - 图衔子形状信息构造
- RectangleRange() - 矩形区域构造
- RectangleRange(double, double, double, double) - 矩形区域构造
- ConvertBoundingBoxToPosition() - 边界框转位置
- Size() - 尺寸构造
- Size(double, double) - 尺寸构造
- RectanglPosition() - 矩形位置构造
- RectanglPosition(double, double, double, double, double) - 矩形位置构造
- ConvertPositionToBoundingBox() - 位置转边界框

### ETVisio文本操作 - 文本处理功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_Text.cs
**功能**: Visio形状文本的查找、替换和设置
**主要类**: ETVisioHelper2013_Text (静态扩展类)
**核心方法**:
- FindShapesStartingWithText(dynamic, List<(string, string)>) - 查找指定开头文本形状
- ReplaceText(Shape, string, Range) - 替换文本用Excel单元格
- ReplaceText(Shape, string, Range, int, int) - 替换文本用Excel范围
- ReplaceText(Shape, string, string) - 替换文本用字符串
- SetShapeText(Shape, Range) - 设置形状文本用Excel单元格
- SetShapeText(Shape, Range, int, int) - 设置形状文本用Excel范围
- SetShapeText(Shape, string) - 设置形状文本用字符串

### ETVisio图衔图框 - 图衔操作功能
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioHelper2013_图衔图框.cs
**功能**: Visio图衔和图框的复制、删除和位置操作
**主要类**: ETVisioHelper2013_图衔图框 (静态类)
**核心方法**:
- Copy图衔AllPages(Document, Document, Range) - 复制所有页面图衔
- Copy图衔SinglePage(Page, Page, Range) - 复制单页图衔
- CreateFilteredDictionary(Dictionary<string, RectanglPosition>, Range) - 创建过滤字典
- Delete图衔子信息(Document, Dictionary<string, RectanglPosition>) - 删除图衔子信息
- Delete图衔子信息ForSinglePage(Page, Dictionary<string, RectanglPosition>) - 删除单页图衔子信息
- Find查找最后一个包含图衔的背景页面(Page) - 查找包含图衔背景页
- Get背景页图衔的位置(dynamic) - 获取背景页图衔位置
- Get图衔范围_BL(Page) - 获取图衔范围BL
- Get图衔子图形在页面中的位置_BL(Page, RectanglPosition) - 获取图衔子图形位置BL

### ETVisio监控器 - 文件监控和转换
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/ETVisioMonitor.cs
**功能**: Visio文件监控和批量PDF转换管理
**主要类**: ETVisioMonitor (实例类)
**核心方法**:
- ETVisioMonitor(ETVisioToPdfConverter) - 监控器构造
- Dispose() - 释放资源
- AddAllFilesAndConvertAsync() - 添加文件并转换异步
- ConvertDictionaryFiles(bool, CancellationToken) - 转换字典文件
- RunConvertDictionaryFilesInThread(bool, CancellationToken) - 多线程转换字典文件
- CancelConversion() - 取消转换
- ResetCancellation() - 重置取消状态
- DeleteNonexistentPdfFilesAsync() - 删除不存在PDF文件异步
- DeleteEmptySubdirectories() - 删除空子目录
- DeleteEmptySubdirectories(string) - 删除指定路径空子目录

### ETVisio PDF转换器 - PDF转换核心
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/HHVisioToPdfConverter.cs
**功能**: Visio文档到PDF的转换核心功能
**主要类**: ETVisioToPdfConverter (实例类)
**核心方法**:
- TempDirectory (属性) - 临时目录属性
- ETVisioToPdfConverter() - PDF转换器构造
- Dispose() - 释放资源
- ConvertFileToPdfAsync(string, string) - 异步转换文件到PDF
- ConvertFileToPdf(string, string) - 转换文件到PDF

### ETVisio函数摘要 - 功能概览
**路径**: ExtensionsTools/ExtensionsTools/ETVisio/VisioExtensions函数摘要.cs
**功能**: Visio扩展功能的完整函数摘要和概览
**主要类**: VisioExtensions函数摘要 (注释文档)
**核心方法**: (58个函数摘要，包含应用程序管理、文档操作、绘图功能、形状操作、文本处理、页面管理、Excel集成、高级功能、PDF导出等完整功能概览)

## 📊 批次4完成情况

**已完成**: 批次4 - Office集成模块全部文件 (16个文件)
**完成时间**: 2025-01-13
**ETWord模块**: 1个文件 (6个方法)
**ETPowerPoint模块**: 1个文件 (7个方法)
**ETVisio模块**: 14个文件 (约150个方法)
**方法总数**: 约163个public方法

---

## 🌐 登录浏览器模块

#### ETLoginWebBrowser - WebView2登录浏览器
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/ETLoginWebBrowser.cs
**功能**: 基于WebView2的登录浏览器，支持HTTP标头和Cookie捕获
**主要类**: ETLoginWebBrowser (窗体类)
**核心方法**:
- ETLoginWebBrowser() - 初始化浏览器
- ETLoginWebBrowser(string) - 初始化并设置URL
- RefreshHeadersAsync() - 刷新HTTP标头
- GetAllCapturedRequestsJson() - 获取所有请求JSON
- GetApiRequestsJson() - 获取API请求JSON
- ClearCapturedRequests() - 清空捕获请求
- NavigateAsync(string) - 导航到指定URL

#### ETLoginWebBrowserFactory - 登录浏览器工厂
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/ETLoginWebBrowserFactory.cs
**功能**: 提供登录浏览器的静态工厂方法和实用工具
**主要类**: ETLoginWebBrowserFactory (静态类)
**核心方法**:
- GetHeadersJson(string, Form) - 获取HTTP标头JSON
- GetCookiesJson(string, Form) - 获取Cookies JSON
- GetCookiesJsonAsync(string, Form) - 异步获取Cookies
- GetCookieData(string, Form) - 获取Cookie数据对象
- GetCookieDataAsync(string, Form) - 异步获取Cookie数据
- CreateLoginBrowser(string) - 创建浏览器实例
- ShowLoginBrowser(...) - 显示登录浏览器
- ShowLoginBrowserAsync(...) - 异步显示浏览器
- IsValidUrl(string) - 验证URL格式
- GetDomain(string) - 获取URL域名
- IsHttpsUrl(string) - 检查HTTPS URL
- BuildUrl(string, string) - 构建完整URL
- CreateWithOptions(LoginBrowserOptions) - 使用配置创建

#### ETLoginWebBrowserTest - 登录浏览器测试
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/ETLoginWebBrowserTest.cs
**功能**: 提供登录浏览器功能的测试方法
**主要类**: ETLoginWebBrowserTest (静态类)
**核心方法**:
- TestBasicUsage() - 测试基本功能
- TestManualNavigation() - 测试手动导航
- TestRefreshCookies() - 测试刷新功能
- RunAllTests() - 运行所有测试

#### ETWebBrowserJsonFormatter - JSON格式化器
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/ETWebBrowserJsonFormatter.cs
**功能**: 处理登录信息的JSON格式化和解析
**主要类**: ETWebBrowserJsonFormatter (静态类)
**核心方法**:
- CreateLoginInfoJson(...) - 创建登录信息JSON
- CreateLoginInfoJsonFromWebViewAsync(...) - 从WebView创建JSON
- IsStandardFormat(string) - 验证JSON标准格式
- ParseLoginInfoJson(string) - 解析登录信息JSON
- IsNewFormat(string) - 验证新格式(已废弃)
- ConvertOldFormatToNewJson(...) - 转换老格式(已废弃)

#### WebView2DiagnosticTool - WebView2诊断工具
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/WebView2DiagnosticTool.cs
**功能**: 提供WebView2运行时的诊断和故障排除功能
**主要类**: WebView2DiagnosticTool (静态类)
**核心方法**:
- RunFullDiagnostic() - 运行完整诊断
- ShowDiagnosticDialog(Form) - 显示诊断对话框

#### WebView2Helper - WebView2助手
**路径**: ExtensionsTools/ExtensionsTools/ETLoginWebBrowser/WebView2Helper.cs
**功能**: 提供WebView2运行时检查和环境创建的辅助功能
**主要类**: WebView2Helper (静态类)
**核心方法**:
- IsWebView2RuntimeAvailable() - 检查运行时可用性
- GetWebView2RuntimeVersion() - 获取运行时版本
- CreateUserDataFolderWithPermissions(string) - 创建用户数据文件夹
- HasWritePermission(string) - 检查写入权限
- ShowWebView2ErrorDialog(Exception, Form) - 显示错误对话框
- GetWebView2ErrorMessage(Exception) - 获取错误信息
- CreateWebView2EnvironmentSafelyAsync(...) - 安全创建环境

---

## 📊 批次5完成总结

---

## 🔧 工具类模块

#### ETAutoResetLabel - 自动重置标签控件
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETAutoResetLabel.cs
**功能**: 可在指定时间后自动恢复默认文本的标签控件
**主要类**: ETAutoResetLabel (继承自Label)
**核心方法**:
- ETAutoResetLabel() - 构造函数
- DefaultText { get; set; } - 默认文本属性
- DefaultDelay { get; set; } - 重置延迟属性
- Text { get; set; } - 标签文本属性

#### ETFileAnalyzer - 文件分析器
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETFileAnalyzer.cs
**功能**: 分析指定时间范围内修改过的文件
**主要类**: ETFileAnalyzer (静态类)
**核心方法**:
- GetModifiedFiles(string, DateTime, DateTime, string, SearchOption) - 获取修改文件
- FormatFileList(List<(string, string)>, DateTime, DateTime, string) - 格式化文件列表

#### ETFileCopier - 文件复制器
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETFileCopier.cs
**功能**: 支持定时和条件过滤的文件复制工具
**主要类**: ETFileCopier (实例类)
**核心方法**:
- CreationTimeLimit { get; set; } - 创建时间限制
- ModificationTimeLimit { get; set; } - 修改时间限制
- MessagesOutput - 消息输出委托
- CopyFiles(string) - 复制文件
- StartCopying(string) - 立即开始复制
- ScheduleCopying(string) - 定时开始复制
- CancelCopying() - 取消复制操作
- CancelScheduledCopying() - 取消定时复制

#### ETNotificationHelper - 通知助手
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETNotificationHelper.cs
**功能**: 显示桌面通知消息和播放提示音
**主要类**: ETNotificationHelper (静态类)
**核心方法**:
- DefaultSoundFilePath { get; set; } - 默认提示音路径
- ShowNotification(string, bool, bool, string) - 显示通知

#### ETUcFileSelect - 文件选择控件
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETUcFileSelect.cs
**功能**: 带历史记录的文件/文件夹选择用户控件
**主要类**: ETUcFileSelect (继承自UserControl)
**核心方法**:
- PathSelectedHandler - 路径选择委托
- FileFilter { get; set; } - 文件过滤器
- ETUcFileSelect() - 构造函数
- SavePathHistoryToFile() - 保存路径历史

#### ETWxPusherService - 微信推送服务
**路径**: ExtensionsTools/ExtensionsTools/ETTools/ETWxPusherService.cs
**功能**: WxPusher平台消息推送服务
**主要类**: ETWxPusherService (静态类)
**核心方法**:
- SendMessageAsync(string, string, string, string[], string, string) - 异步发送消息

---

## 🔌 API集成模块

#### ETEverythingConfig - Everything搜索配置
**路径**: ExtensionsTools/ExtensionsTools/ETAPI/ETEverythingConfig.cs
**功能**: Everything搜索服务配置管理
**主要类**: ETEverythingConfig (配置类)
**核心方法**:
- GetConfigFilePath() - 获取配置文件路径
- LoadConfig() - 从配置文件加载配置
- SaveConfig(ETEverythingConfig, string) - 保存配置到文件
- GetEverythingServerAddress() - 获取服务器地址

#### ETEverythingSearch - Everything搜索服务
**路径**: ExtensionsTools/ExtensionsTools/ETAPI/ETEverythingSearch.cs
**功能**: Everything文件搜索API集成服务
**主要类**: ETEverythingSearch (搜索服务类)
**核心方法**:
- ETEverythingSearch() - 默认构造函数
- ETEverythingSearch(ETEverythingConfig) - 配置构造函数
- SaveConfig() - 保存当前配置
- SearchAsync(string, int) - 异步搜索
- SearchAsync(string, int, int) - 异步搜索带超时
- Search(string, int) - 同步搜索
- Search(string, int, int) - 同步搜索带超时
- SearchExample(string, int) - 搜索示例
- SearchExampleAsync(string, int) - 异步搜索示例
- Search(string, string, int, int) - 静态搜索方法
- SearchExample(string, string, int) - 静态搜索示例
- SearchExampleAsync(string, string, int) - 静态异步搜索示例

#### ETEverythingSearchExample - Everything搜索示例
**路径**: ExtensionsTools/ExtensionsTools/ETAPI/ETEverythingSearchExample.cs
**功能**: Everything搜索功能使用示例
**主要类**: ETEverythingSearchExample (静态示例类)
**核心方法**:
- UsageExample() - 使用示例
- IniConfigExample() - INI配置示例

#### ETEverythingSearchResult - Everything搜索结果
**路径**: ExtensionsTools/ExtensionsTools/ETAPI/ETEverythingSearchResult.cs
**功能**: Everything搜索结果数据模型
**主要类**: ETEverythingSearchResult (结果类)
**核心方法**:
- ETEverythingSearchResult(string, string, bool, string, DateTime) - 构造函数
- ToString() - 字符串表示

---

## 🤖 AI功能模块

#### AIAssistant - AI助手
**路径**: ExtensionsTools/ExtensionsTools/ETAI/AIAssistant.cs
**功能**: Excel AI助手核心功能，提供AI问答和批处理
**主要类**: AIAssistant (静态AI助手类)
**核心方法**:
- CancelAllRequests() - 取消所有AI请求
- BatchProcessAIResponsesToExcelAsync(Range, Range, Range, string, string, string, Action<JObject, bool>) - 批处理AI响应到Excel
- GetAIResponseAsync(List<AIQuestionGroup>, string, string, string, Action<JObject, bool>) - 获取AI响应
- GetSingleQuestionResponseAsync(string, string, string, string) - 获取单问题响应
- FillResponseToExcel(Range, Range, JObject, bool) - 填充响应到Excel

#### AIExcelAssistant - Excel AI助手v2
**路径**: ExtensionsTools/ExtensionsTools/ETAIv2/AIExcelAssistant.cs
**功能**: 新版Excel AI助手，提供增强的AI处理能力
**主要类**: AIExcelAssistant (AI助手类)
**核心方法**:
- AIExcelAssistant() - 构造函数
- ProcessExcelDataAsync(Range, Range, Range, Range, string, string, CancellationToken) - 处理Excel数据
- BatchProcessAIResponsesToExcelAsync(Range, Range, Range, string, string, string, Action<JObject, bool>) - 批处理兼容方法
- Dispose() - 释放资源

---

## 🌍 地理位置模块

#### ETGPS - GPS处理工具
**路径**: ExtensionsTools/ExtensionsTools/ETGeographic/ETGPS.cs
**功能**: GPS坐标处理、KML文件生成、坐标转换、距离计算
**主要类**: ETGPS (静态类), CoordinateDistance (距离计算器)
**核心方法**:
- GeneratePointKmlFile(List<XlGpsKmlPoint>, string) - 生成点KML文件
- GenerateKmlPolygonFile(List<XlGpsKmlPolygon>, string) - 生成多边形KML文件
- DecimalToDms(double, int) - 十进制转DMS格式
- DmsToDecimal(string) - DMS转十进制格式
- ExtractGps(Range) - 从Excel提取GPS点
- ExtractGps(string) - 从字符串提取GPS点
- IsLongitude(double) - 验证经度有效性
- IsLatitude(double) - 验证纬度有效性
- ExtractGpsList(Range) - 提取GPS点列表
- FindClosestPoints(List<XlGpsPointAndRange>, XlGpsPoint, int) - 查找最近点
- GetDistance(double, double, double, double) - 计算两点距离
- CoordinateDistance(double, double) - 距离计算器构造
- Distance(Coordinate) - 计算坐标距离

#### ETGpsConvertUtil - GPS坐标转换工具
**路径**: ExtensionsTools/ExtensionsTools/ETGeographic/ETGpsConvertUtil.cs
**功能**: GPS坐标系转换，支持WGS84、百度坐标、火星坐标转换
**主要类**: ETGpsConvertUtil (坐标转换工具)
**核心方法**:
- wgs84tobd09(double, double) - WGS84转百度坐标
- wgs84tobd09(double[][]) - 批量WGS84转百度坐标
- gcj02towgs84(double, double) - 火星坐标转WGS84
- gcj02towgs84(double[][]) - 批量火星坐标转WGS84

#### ETPolygonUtil - 多边形处理工具
**路径**: ExtensionsTools/ExtensionsTools/ETGeographic/ETPolygonUtil.cs
**功能**: 多边形几何计算、点与多边形关系判断、KML文件处理
**主要类**: ETPolygonUtil (静态多边形工具类)
**核心方法**:
- IsPointInPolygon(double, double, string) - 判断点是否在多边形内
- CalculateDistanceToPolygon(double, double, string) - 计算点到多边形距离
- ExtractPolygonData(ExcelRange, ExcelRange) - 从Excel提取多边形数据
- FindNearestPolygon(double, double, List<(string, string)>) - 查找最近多边形
- FindNearestPolygonOptimized(double, double, List<(string, string)>) - 优化版最近多边形查找
- FindNearestPolygonWithGroups(double, double, List<(string, string)>) - 分组查找最近多边形
- FindNearestPolygonsBatch(List<(double, double)>, List<(string, string)>) - 批量查找最近多边形
- LoadPolygonsFromKmlFile(string) - 从KML文件加载多边形
- ParsePolygonData(string) - 解析多边形数据

---

## 📈 股票数据模块

#### AkToolsClient - AkTools API客户端
**路径**: ExtensionsTools/ExtensionsTools/ETStock/AkToolsClient.cs
**功能**: AkTools股票数据API客户端，提供HTTP请求封装
**主要类**: AkToolsClient (API客户端类)
**核心方法**:
- AkToolsClient(string) - 构造函数，设置API基础URL
- GetDataAsync<T>(string, Dictionary<string, string>) - 异步获取数据
- Dispose() - 释放HttpClient资源

#### AkToolsClientStockDataCrawler - 股票数据爬虫
**路径**: ExtensionsTools/ExtensionsTools/ETStock/AkToolsClientStockDataCrawler.cs
**功能**: 股票数据爬取，获取股票代码、交易日历等信息
**主要类**: AkToolsClientStockDataCrawler (静态爬虫类)
**核心方法**:
- GetAllStockCodesAsync() - 获取所有股票代码
- GetTradeDateHistoryAsync(int) - 获取交易日历历史
- GetLatestTradeDateAsync() - 获取最新交易日期

#### StockHelper - 股票助手工具
**路径**: ExtensionsTools/ExtensionsTools/ETStock/StockHelper.cs
**功能**: 股票代码验证和处理工具
**主要类**: StockHelper (静态助手类)
**核心方法**:
- IsValidStockCode(string) - 验证股票代码有效性

#### StockSoftwareCommand - 股票软件命令
**路径**: ExtensionsTools/ExtensionsTools/ETStock/StockSoftwareCommand.cs
**功能**: 股票软件窗口控制和命令发送
**主要类**: StockSoftwareCommand (静态命令类)
**核心方法**:
- JumpToStock(string, StockSoftwareType) - 跳转到指定股票
- JumpToStock(string, StockSoftwareType, int) - 跳转到指定股票带延时
- IsStockSoftwareRunning(StockSoftwareType) - 检查股票软件是否运行

---

## 📡 通信服务模块

#### ETCommunicationService - 通信服务
**路径**: ExtensionsTools/ExtensionsTools/ETCommunicationService/ETCommunicationService.cs
**功能**: TCP通信服务，支持客户端/服务器模式，消息传输和文件传输
**主要类**: ETCommunicationService (通信服务类)
**核心方法**:
- ETCommunicationService(bool, int, bool, string) - 构造函数，初始化通信服务
- StartAsync() - 异步启动通信服务
- Stop() - 停止通信服务
- ConnectAsync(string) - 异步连接到服务器
- Disconnect() - 断开连接
- SendCommandAsync(string) - 发送纯指令消息
- SendCommandWithTextAsync(string, string) - 发送带文本的指令消息
- SendCommandWithFileAsync(string, string) - 发送带文件的指令消息
- MessageReceivedEventHandler(CommunicationMessage) - 消息接收事件委托
- ConnectionStateChangedEventHandler(bool) - 连接状态变化事件委托

---

## 🔧 通用工具类模块

#### AES - AES加密工具
**路径**: ExtensionsTools/Common.Utility/AES.cs
**功能**: AES加密解密、哈希生成、MD5计算
**主要类**: AES (加密工具类)
**核心方法**:
- encrypt(string, string) - AES加密
- decrypt(string, string) - AES解密
- RegCode16r(string) - 生成16位注册码
- MD5(string) - MD5哈希计算

#### FileOperate - 文件操作工具
**路径**: ExtensionsTools/Common.Utility/FileOperate.cs
**功能**: 文件和目录操作的综合工具类
**主要类**: FileOperate (静态文件操作类)
**核心方法**:
- GetPostfixStr(string) - 获取文件后缀名
- WriteFile(string, string) - 写入文件
- ReadFile(string) - 读取文件
- FileAdd(string, string) - 追加文件内容
- FileCoppy(string, string) - 复制文件
- FileDel(string) - 删除文件
- FileMove(string, string) - 移动文件
- FolderCreate(string, string) - 创建文件夹
- FolderCreate(string) - 创建文件夹
- FileCreate(string) - 创建文件
- DeleteFolder(string) - 递归删除文件夹
- CopyDir(string, string) - 复制目录
- GetFoldAll(string) - 获取目录所有内容
- ListTreeShow(DirectoryInfo, int, string) - 递归显示目录树
- GetFoldAll(string, string, string) - 获取目录内容下拉框
- ListTreeShow(DirectoryInfo, int, string, string) - 递归显示目录树带模板
- GetDirectoryLength(string) - 获取文件夹大小
- GetFileAttibe(string) - 获取文件详细属性
- IsExistDirectory(string) - 检查目录是否存在
- IsExistFile(string) - 检查文件是否存在
- GetFileNames(string) - 获取目录中所有文件
- GetDirectories(string) - 获取所有子目录
- GetFileNames(string, string, bool) - 获取文件列表带搜索
- IsEmptyDirectory(string) - 检查目录是否为空
- Contains(string, string) - 检查目录是否包含文件
- Contains(string, string, bool) - 检查目录是否包含文件带子目录搜索
- GetDateDir() - 根据时间获取目录名
- GetDateFile() - 根据时间获取文件名
- CopyFolder(string, string) - 复制文件夹
- GetFileName(string) - 从路径获取文件名
- CreateFile(string, byte[]) - 创建文件并写入字节流
- GetLineCount(string) - 获取文本文件行数
- GetFileSize(string) - 获取文件大小
- GetDirectories(string, string, bool) - 获取目录列表带搜索
- WriteText(string, string, Encoding) - 写入文本文件
- AppendText(string, string) - 追加文本到文件
- Copy(string, string) - 复制文件
- GetFileNameNoExtension(string) - 获取不含扩展名的文件名
- GetExtension(string) - 获取文件扩展名
- DeleteDirectory(string) - 删除目录及子目录
- CreateDirectory(string) - 创建目录
- directoryCopy(string, string) - 目录复制

#### HWInfo - 硬件信息获取工具
**路径**: ExtensionsTools/Common.Utility/HWInfo.cs
**功能**: 获取计算机硬件信息，包括CPU、主板、硬盘、网卡等
**主要类**: HWInfo (硬件信息类)
**核心方法**:
- getComputerInfo() - 获取计算机综合信息
- DeviceIoControl(IntPtr, uint, IntPtr, uint, IntPtr, uint, ref uint, IntPtr) - 设备IO控制
- CloseHandle(IntPtr) - 关闭句柄
- GetNicAddress(string) - 获取网卡地址
- GetCpuID() - 获取CPU编号
- GetMainBoard() - 获取主板编号
- GetBiosSN() - 获取BIOS序列号
- GetHDSN() - 获取硬盘序列号
- GetAllNic() - 获取所有网卡

#### ValidatorHelper - 验证助手工具
**路径**: ExtensionsTools/Common.Utility/ValidatorHelper.cs
**功能**: 各种数据格式验证工具
**主要类**: ValidatorHelper (静态验证类)
**核心方法**:
- IsEmail(string) - 验证邮箱格式
- IsUrl(string) - 验证网址格式
- IsDateTime(string) - 验证日期格式
- IsMobile(string) - 验证手机号格式
- IsIP(string) - 验证IP地址格式
- IsIDCard(string) - 验证身份证号
- IsIDCard18(string) - 验证18位身份证
- IsIDCard15(string) - 验证15位身份证
- IsInt(string) - 验证整数格式
- IsLengthStr(string, int, int) - 验证字符串长度
- IsTel(string) - 验证电话号码
- IsPostCode(string) - 验证邮政编码
- IsChinese(string) - 验证中文字符
- IsNormalChar(string) - 验证正常字符
- checkUserId(string) - 验证用户名格式
- IsDate(string) - 验证日期格式
- IsNumber(string) - 验证数字格式
- IsDecimal(string) - 验证小数格式
- IsHanyu(string) - 验证是否包含汉语
- IsHanyuAll(string) - 验证是否全部汉语

---

#### IntPtrEnumHelper - 指针枚举助手
**路径**: ExtensionsTools/Common.Utility/class/IntPtrEnumHelper.cs
**功能**: 提供指针和枚举标志位操作功能
**主要类**: IntPtrEnumHelper, EnumHelper, WindowStyleHelper (静态类)
**核心方法**:
- HasFlags(IntPtr, object) - 检查标志位
- SetFlag(IntPtr, object) - 设置标志位
- UnsetFlag(IntPtr, object) - 取消标志位
- HasFlag(long, long) - 检查长整型标志位
- SetFlag(long, long) - 设置长整型标志位
- UnsetFlag(long, long) - 取消长整型标志位
- SetWindowLong32(IntPtr, int, IntPtr) - 设置窗口属性32位
- SetWindowLongPtr(IntPtr, WindowLongFlags, IntPtr) - 设置窗口属性指针
- SetWindowStyles(IntPtr, WindowStyles) - 设置窗口样式
- UnsetWindowStyles(IntPtr, WindowStyles) - 取消窗口样式
- SetWindowStylesEx(IntPtr, WindowStylesEx) - 设置扩展窗口样式
- UnsetWindowStylesEx(IntPtr, WindowStylesEx) - 取消扩展窗口样式

#### Win32Window - Win32窗口包装类
**路径**: ExtensionsTools/Common.Utility/class/Win32Window.cs
**功能**: 提供Win32窗口句柄包装
**主要类**: Win32Window (实例类)
**核心方法**:
- Win32Window(int) - 构造函数

#### struct - 结构定义文件
**路径**: ExtensionsTools/Common.Utility/class/struct.cs
**功能**: 定义系统结构和枚举
**主要类**: 结构定义文件
**核心方法**: 无public方法

#### Enums - 枚举定义文件
**路径**: ExtensionsTools/Common.Utility/系统/Enums.cs
**功能**: 定义系统枚举类型
**主要类**: 枚举定义文件
**核心方法**: 无public方法

#### Structs - 结构体定义文件
**路径**: ExtensionsTools/Common.Utility/系统/Structs.cs
**功能**: 定义系统结构体类型
**主要类**: BITMAPINFOHEADER, BITMAPINFO等
**核心方法**:
- biSize - 位图信息头大小
- bmiHeader - 位图信息头

#### Win32API - Win32 API调用集合
**路径**: ExtensionsTools/Common.Utility/系统/Win32API.cs.cs
**功能**: 提供全面的Win32 API调用功能
**主要类**: Win32API (静态类)
**核心方法**:
- HookProc(int, IntPtr, IntPtr) - 钩子回调
- GetCurrentThreadId() - 获取当前线程ID
- StretchBlt(IntPtr, int, int, int, int, IntPtr, int, int, int, int, uint) - 拉伸位块传输
- SetWindowTheme(IntPtr, string, string) - 设置窗口主题
- GetDC(IntPtr) - 获取设备上下文
- InitCommonControlsEx(INITCOMMONCONTROLSEX) - 初始化通用控件
- GetWindowThreadProcessId(IntPtr, out int) - 获取窗口线程进程ID
- GET_X_LPARAM(int) - 获取X坐标
- [包含约120个Win32 API函数调用]

---

**🎯 索引完成情况**: 🎉 已完成全部12个批次，共141个文件，完成率100%
**📝 最后更新**: 2025-01-14
**✅ 任务状态**: ExtensionsTools功能模块函数索引生成任务圆满完成！
