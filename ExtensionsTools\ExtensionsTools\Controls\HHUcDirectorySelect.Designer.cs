﻿namespace ET.Controls
{
    partial class HHUcDirectorySelect
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.splitContainer1 = new System.Windows.Forms.SplitContainer();
            this.textBox路径 = new System.Windows.Forms.RichTextBox();
            this.button选择 = new System.Windows.Forms.Label();
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).BeginInit();
            this.splitContainer1.Panel1.SuspendLayout();
            this.splitContainer1.Panel2.SuspendLayout();
            this.splitContainer1.SuspendLayout();
            this.SuspendLayout();
            // 
            // splitContainer1
            // 
            this.splitContainer1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
            this.splitContainer1.Location = new System.Drawing.Point(210, 77);
            this.splitContainer1.Margin = new System.Windows.Forms.Padding(0);
            this.splitContainer1.Name = "splitContainer1";
            // 
            // splitContainer1.Panel1
            // 
            this.splitContainer1.Panel1.Controls.Add(this.textBox路径);
            // 
            // splitContainer1.Panel2
            // 
            this.splitContainer1.Panel2.Controls.Add(this.button选择);
            this.splitContainer1.Panel2.Click += new System.EventHandler(this.Button选择_Click);
            this.splitContainer1.Size = new System.Drawing.Size(290, 97);
            this.splitContainer1.SplitterDistance = 178;
            this.splitContainer1.SplitterWidth = 1;
            this.splitContainer1.TabIndex = 4;
            this.splitContainer1.SizeChanged += new System.EventHandler(this.SplitContainer1_SizeChanged);
            // 
            // textBox路径
            // 
            this.textBox路径.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.textBox路径.Dock = System.Windows.Forms.DockStyle.Fill;
            this.textBox路径.Location = new System.Drawing.Point(0, 0);
            this.textBox路径.Multiline = false;
            this.textBox路径.Name = "textBox路径";
            this.textBox路径.ScrollBars = System.Windows.Forms.RichTextBoxScrollBars.None;
            this.textBox路径.Size = new System.Drawing.Size(176, 95);
            this.textBox路径.TabIndex = 1;
            this.textBox路径.Text = "";
            this.textBox路径.TextChanged += new System.EventHandler(this.TextBox路径_TextChanged);
            // 
            // button选择
            // 
            this.button选择.Dock = System.Windows.Forms.DockStyle.Fill;
            this.button选择.Location = new System.Drawing.Point(0, 0);
            this.button选择.Name = "button选择";
            this.button选择.Size = new System.Drawing.Size(109, 95);
            this.button选择.TabIndex = 0;
            this.button选择.Text = "∷";
            this.button选择.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.button选择.Click += new System.EventHandler(this.Button选择_Click);
            // 
            // ucDirectorySelect
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.splitContainer1);
            this.Name = "ucDirectorySelect";
            this.Size = new System.Drawing.Size(710, 250);
            this.SizeChanged += new System.EventHandler(this.UcExcelRangeSelect_SizeChanged);
            this.splitContainer1.Panel1.ResumeLayout(false);
            this.splitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.splitContainer1)).EndInit();
            this.splitContainer1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private System.Windows.Forms.SplitContainer splitContainer1;
        private System.Windows.Forms.RichTextBox textBox路径;
        private System.Windows.Forms.Label button选择;
    }
}
