<ProjectData xmlns="http://schemas.datacontract.org/2004/07/BG8.Supercharger.Features.CodeMap.GlobalHistory" xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><ProjectGlobalHistoryData xmlns:a="http://schemas.microsoft.com/2003/10/Serialization/Arrays"><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\ChinaTowerHttpService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Services.ChinaTowerHttpService.PostAsync#Task&lt;string&gt;#string, string, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PostAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Services.ChinaTowerHttpService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName><TimeStamp>2025-07-09T11:58:02.1885143+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\ChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Forms\TabConfigFormV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Forms.TabConfigFormV2.ValidateConfigAsync#Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ValidateConfigAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Forms.TabConfigFormV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:40:22.48073+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Forms\TabConfigFormV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserMenuOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.GetSessionStatusInfo#string#object</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetSessionStatusInfo</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName><TimeStamp>2025-07-15T23:00:15.406553+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ViewSessionStatusMenuItem_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ViewSessionStatusMenuItem_Click</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:40:22.4477655+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserMenuOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.InitializeManagersSafely#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeManagersSafely</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:41:08.0592288+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.DisposeManagersSafely#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DisposeManagersSafely</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:41:05.3168589+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ConfigManager</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConfigManager</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:40:41.7075463+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\PathPreviewForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.PathPreviewForm.BtnCreateAndOpen_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BtnCreateAndOpen_Click</ItemName><ItemPath>HyAssistant.NewFolder.PathPreviewForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\PathPreviewForm.cs</ProjectItemFileName><TimeStamp>2025-07-23T11:13:07.8891979+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\PathPreviewForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserUIOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.UpdateUIForEmptyTabs#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateUIForEmptyTabs</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:40:22.417805+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.UpdateAddressBar#void#WebBrowserTabManagerV2.TabData</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateAddressBar</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName><TimeStamp>2025-07-15T21:26:29.6085385+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserUIOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.AsyncExceptionHandlerV2</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>AsyncExceptionHandlerV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:59:15.8940667+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.AsyncExceptionHandlerV2.HandleWebView2Exceptionstatic##void#object, CoreWebView2Exception, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HandleWebView2Exception</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.AsyncExceptionHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:54:38.3152062+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.AsyncExceptionHandlerV2.ExecuteSafelyAsyncstatic##Task&lt;T&gt;#Func&lt;Task&lt;T&gt;&gt;, string, object, TimeSpan?, CancellationToken</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteSafelyAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.AsyncExceptionHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:54:35.6593562+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\AsyncExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Managers.WebBrowserTabManagerV2.CloseTabAsync#Task&lt;bool&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CloseTabAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Managers.WebBrowserTabManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T20:54:06.8670314+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserTabManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\LoggingHelperV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.LoggingHelperV2.LogAsyncException#void#Exception, string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LogAsyncException</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.LoggingHelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T21:09:18.1565075+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\LoggingHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Configuration.ChinaTowerConfig.PhotoUrl</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>PhotoUrl</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Configuration.ChinaTowerConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName><TimeStamp>2025-07-05T21:54:03.5644977+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Configuration\ChinaTowerConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.MainForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyAssistant.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.Designer.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:04:09.8950823+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserCookieManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserCookieManager.GetCookiesFromWebView2InternalAsync#Task&lt;CookieData&gt;#WebView2</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetCookiesFromWebView2InternalAsync</ItemName><ItemPath>HyAssistant.WebBrowserCookieManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:45:43.7868221+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserCookieManager.GetCookiesFromWebView2Async#Task&lt;CookieData&gt;#WebView2</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetCookiesFromWebView2Async</ItemName><ItemPath>HyAssistant.WebBrowserCookieManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T22:00:16.6338911+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserCookieManager.WebView_WebResourceRequested#void#object, CoreWebView2WebResourceRequestedEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebView_WebResourceRequested</ItemName><ItemPath>HyAssistant.WebBrowserCookieManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:39:15.9124617+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserCookieManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.BoundaryAndExceptionTesterV2.TestResourceExhaustionScenariosAsync#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TestResourceExhaustionScenariosAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.BoundaryAndExceptionTesterV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T20:38:10.0145157+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowser.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowser.PasteCookiesButton_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PasteCookiesButton_Click</ItemName><ItemPath>HyAssistant.WebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowser.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:04:33.755031+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowser.CopyCookiesConfigButton_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CopyCookiesConfigButton_Click</ItemName><ItemPath>HyAssistant.WebBrowser</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowser.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:58:46.2900452+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowser.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\MenuGenerator.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.MenuGenerator</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>MenuGenerator</ItemName><ItemPath>HyAssistant.NewFolder</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\MenuGenerator.cs</ProjectItemFileName><TimeStamp>2025-07-13T21:22:08.3686524+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\MenuGenerator.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.CookieManagerFormTransferTesterV2.RunCompleteTestAsync#Task&lt;TestSummary&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>RunCompleteTestAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.CookieManagerFormTransferTesterV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T20:38:50.1502455+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserUIHelperV2.UpdateRefreshStatusLabelsInternalstatic##void#StatusStrip, WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateRefreshStatusLabelsInternal</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserUIHelperV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T23:38:46.6500461+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserUIHelperV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\FormClosingHandlerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.FormClosingHandlerV2.FormClosingHandlerV2##WebBrowserV2</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FormClosingHandlerV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.FormClosingHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:20:28.8924636+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\FormClosingHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.HaPermissionKeys.WebBrowserV2</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WebBrowserV2</ItemName><ItemPath>HyAssistant.HaPermissionKeys</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:05:25.8864764+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.HaPermissionKeys.WebBrowserv2</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WebBrowserv2</ItemName><ItemPath>HyAssistant.HaPermissionKeys</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:05:03.2155134+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.HaPermissionKeys.WebBrowser</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WebBrowser</ItemName><ItemPath>HyAssistant.HaPermissionKeys</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:05:02.2098568+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.HaPermissionKeys</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HaPermissionKeys</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:04:49.8713813+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaPermissionKeys.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\IChinaTowerHttpService.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Services.IChinaTowerHttpService.TestAuthorizationOnlyPostAsync#Task&lt;string&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>TestAuthorizationOnlyPostAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Services.IChinaTowerHttpService</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName><TimeStamp>2025-07-03T22:03:42.2018147+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Services\IChinaTowerHttpService.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCacheOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ExecuteCacheClearingAsync#Task#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExecuteCacheClearingAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName><TimeStamp>2025-07-16T20:06:34.5588378+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCacheOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.CookieManagerFormTransferTestExecutorV2.ExecuteCompleteTestAsync#Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ExecuteCompleteTestAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.CookieManagerFormTransferTestExecutorV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T20:39:05.1831802+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieManagerFormTransferTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.ConfigReader.CreateDefaultConfig#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateDefaultConfig</ItemName><ItemPath>HyAssistant.NewFolder.ConfigReader</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-13T23:11:06.9270668+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.ConfigReader.ConfigFileExists#bool#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ConfigFileExists</ItemName><ItemPath>HyAssistant.NewFolder.ConfigReader</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-13T23:07:46.6247523+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.ConfigReader.ValidateConfigFile#bool#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ValidateConfigFile</ItemName><ItemPath>HyAssistant.NewFolder.ConfigReader</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-13T21:22:08.2249916+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.ConfigReader.LoadConfig#List&lt;NewFolderItem&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadConfig</ItemName><ItemPath>HyAssistant.NewFolder.ConfigReader</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:59:45.3590829+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.ConfigReader</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ConfigReader</ItemName><ItemPath>HyAssistant.NewFolder</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:57:21.996203+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\ConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfig.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabConfig.CookiePath</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CookiePath</ItemName><ItemPath>HyAssistant.WebBrowserTabConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfig.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:27:42.6622036+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabConfig</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebBrowserTabConfig</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfig.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:25:58.5079451+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabConfig.Name</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Name</ItemName><ItemPath>HyAssistant.WebBrowserTabConfig</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfig.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:25:57.1954901+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfig.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConfigManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserConfigManager.ReloadConfig#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ReloadConfig</ItemName><ItemPath>HyAssistant.WebBrowserConfigManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName><TimeStamp>2025-07-10T21:46:06.2685803+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserConfigManager.Config</ID><ImageSource>img\tvi\x_property-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Config</ItemName><ItemPath>HyAssistant.WebBrowserConfigManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName><TimeStamp>2025-07-10T21:45:49.8343301+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConfigManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerDownload.TestChinaTowerServerAsync#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TestChinaTowerServerAsync</ItemName><ItemPath>HyAssistant.frmChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T19:36:18.2178647+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerDownload.frmChinaTowerDownload_FormClosed#void#object, FormClosedEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmChinaTowerDownload_FormClosed</ItemName><ItemPath>HyAssistant.frmChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T19:36:16.4769757+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerDownload</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmChinaTowerDownload</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T18:09:37.7163607+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerDownload.TestChinaTowerServer#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TestChinaTowerServer</ItemName><ItemPath>HyAssistant.frmChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T18:04:11.5886031+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerDownload.btnTest_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>btnTest_Click</ItemName><ItemPath>HyAssistant.frmChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T18:04:07.464523+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookiePathManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.CookiePathManagerV2.GenerateSectionBasedPath#string#WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GenerateSectionBasedPath</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.CookiePathManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:13:02.5899748+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookiePathManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.CookieManagerForm.WebView_SourceChanged#void#object, CoreWebView2SourceChangedEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebView_SourceChanged</ItemName><ItemPath>HyAssistant.CookieManagerForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</ProjectItemFileName><TimeStamp>2025-07-15T07:35:08.0270299+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.CookieManagerForm</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CookieManagerForm</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:28:02.0297223+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.CookieManagerForm.HandlePasteOperation#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HandlePasteOperation</ItemName><ItemPath>HyAssistant.CookieManagerForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:05:03.6773535+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.CookieManagerForm.粘贴板内容到Cookies_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>粘贴板内容到Cookies_Click</ItemName><ItemPath>HyAssistant.CookieManagerForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:05:02.0437151+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2.GetUserFriendlyMessagestatic##string#string, Exception</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetUserFriendlyMessage</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T23:06:01.1078219+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2.GetWebView2UserFriendlyMessagestatic##string#CoreWebView2Exception</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetWebView2UserFriendlyMessage</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T23:00:02.9257513+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2.HandleWebView2Exceptionstatic##void#object, Exception, string, bool</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>HandleWebView2Exception</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserExceptionHandlerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:52:06.8705441+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserExceptionHandlerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.GetStationByIdAsync#Task&lt;StationInfoExcerpt&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetStationByIdAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T18:20:54.1484742+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.GetAllStationsAsync#Task&lt;List&lt;StationInfoExcerpt&gt;&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetAllStationsAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T18:20:46.5810012+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.SearchStationsAsync#Task&lt;List&lt;StationInfoExcerpt&gt;&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>SearchStationsAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T18:20:43.7938262+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.StationExistsInternalAsync#Task&lt;bool&gt;#SQLiteConnection, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StationExistsInternalAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T18:01:30.6650106+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.DeleteStationAsync#Task&lt;bool&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>DeleteStationAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T17:57:54.7399583+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.StationRepository.GetStationByCodeAsync#Task&lt;StationInfoExcerpt&gt;#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetStationByCodeAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.StationRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName><TimeStamp>2025-07-03T16:47:19.110393+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\StationRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.FileAnalyzer.BindControlsToIniSettings#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>BindControlsToIniSettings</ItemName><ItemPath>HyAssistant.FileAnalyzer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:30:22.349031+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.FileAnalyzer.InitializeConfigFile#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeConfigFile</ItemName><ItemPath>HyAssistant.FileAnalyzer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:30:15.3944187+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.FileAnalyzer.FileAnalyzer##</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>FileAnalyzer</ItemName><ItemPath>HyAssistant.FileAnalyzer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:22:20.7176333+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.FileAnalyzer.ReleaseResources#void#</ID><ImageSource>img\tvi\x_method-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>ReleaseResources</ItemName><ItemPath>HyAssistant.FileAnalyzer</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:21:45.2522825+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileAnalyzer\FileAnalyzer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserSessionManager.GetTabNameBySectionId#string#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetTabNameBySectionId</ItemName><ItemPath>HyAssistant.WebBrowserSessionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName><TimeStamp>2025-07-09T22:09:36.5882382+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2.InitializeErrorTemplatesstatic##Dictionary&lt;Type, ErrorMessageTemplate&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeErrorTemplates</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:59:21.0064521+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2.GetWebView2ErrorInfostatic##UserFriendlyErrorInfo#CoreWebView2Exception, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetWebView2ErrorInfo</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:59:16.7506838+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2.GetUserFriendlyErrorInfostatic##UserFriendlyErrorInfo#Exception, string</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetUserFriendlyErrorInfo</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.UserFriendlyErrorManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:52:10.468495+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\UserFriendlyErrorManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileCopier\FileCopier.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.FileCopier.checkBoxFileCopier_CheckStateChanged#void#object, EventArgs</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>checkBoxFileCopier_CheckStateChanged</ItemName><ItemPath>HyAssistant.FileCopier</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileCopier\FileCopier.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:20:27.4234234+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.FileCopier.StartCopyInternal#void#CancellationToken</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>StartCopyInternal</ItemName><ItemPath>HyAssistant.FileCopier</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileCopier\FileCopier.cs</ProjectItemFileName><TimeStamp>2025-06-29T15:17:59.8918651+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\FileCopier\FileCopier.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.NewFolderManager.CreateFolderWithPreview#string#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateFolderWithPreview</ItemName><ItemPath>HyAssistant.NewFolder.NewFolderManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName><TimeStamp>2025-07-13T23:46:05.2029455+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.NewFolderManager.EnsureConfigFileExists#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>EnsureConfigFileExists</ItemName><ItemPath>HyAssistant.NewFolder.NewFolderManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName><TimeStamp>2025-07-13T23:36:27.2639878+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.NewFolderManager.Initialize#void#ToolStripMenuItem</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Initialize</ItemName><ItemPath>HyAssistant.NewFolder.NewFolderManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName><TimeStamp>2025-07-13T23:04:48.4928176+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.NewFolderManager.CreateDefaultConfig#void#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateDefaultConfig</ItemName><ItemPath>HyAssistant.NewFolder.NewFolderManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName><TimeStamp>2025-07-13T22:26:45.6773402+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.NewFolderManager</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>NewFolderManager</ItemName><ItemPath>HyAssistant.NewFolder</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName><TimeStamp>2025-07-13T21:01:10.5334968+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\NewFolderManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionKeeper.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserSessionKeeper.DetermineTargetUrl#string#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DetermineTargetUrl</ItemName><ItemPath>HyAssistant.WebBrowserSessionKeeper</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:23:11.2373412+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserSessionKeeper.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Managers.WebBrowserConfigManagerV2.InitializeAsync#Task&lt;bool&gt;#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>InitializeAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Managers.WebBrowserConfigManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:17:26.0823685+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Managers.WebBrowserConfigManagerV2</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebBrowserConfigManagerV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Managers</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T22:16:39.904517+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserConfigManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebBrowserV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T17:32:20.3927966+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.SetCookiesToWebViewAsync#Task&lt;bool&gt;#WebView2, ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetCookiesToWebViewAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T17:08:18.3383189+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.UpdateMenuItemStateSafely#void#string, bool, bool?</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateMenuItemStateSafely</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T17:08:14.9305938+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.UpdateNavigationButtonsStateInternal#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateNavigationButtonsStateInternal</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T15:02:13.033357+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.SafeExecuteWebView2#bool#string, Action&lt;WebView2&gt;, bool</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SafeExecuteWebView2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:45:32.5398285+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.GetAllPerformanceStatistics#string#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetAllPerformanceStatistics</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:45:25.2894767+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ThreadSafeAsyncOperation#Task&lt;bool&gt;#string, Func&lt;CancellationToken, Task&gt;, CancellationToken</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThreadSafeAsyncOperation</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:45:17.5088844+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ParseLoginInfoDataAsync#Task&lt;ET.ETLoginWebBrowser.ETWebBrowserJsonFormatter.LoginInfoData&gt;#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ParseLoginInfoDataAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:45:00.8012316+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ThreadSafeConfigOperation#bool#string, Action, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThreadSafeConfigOperation</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:44:49.4946611+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.PerformanceToken.PerformanceToken##string</ID><ImageSource>img\tvi\x_constructor-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>PerformanceToken</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2.PerformanceToken</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:44:47.7665473+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ThreadSafeUIOperation#bool#string, Action, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ThreadSafeUIOperation</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:44:30.7451273+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ExecuteBatchOperationsAsync#Task&lt;bool&gt;#List&lt;Func&lt;Task&lt;bool&gt;&gt;&gt;, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExecuteBatchOperationsAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:44:23.3545979+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ClearTabControlSafely#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ClearTabControlSafely</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:21:02.0210173+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.GenerateAndCopyConfigurationAsync#Task#WebView2, WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GenerateAndCopyConfigurationAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName><TimeStamp>2025-07-16T14:19:17.0914286+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserEventHandlersV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetHtml#string#string, string, string, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetHtml</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T17:50:31.3304519+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.DownloadPhoto#Task#List&lt;PhotoInfoExcerpt&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>DownloadPhoto</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T17:49:43.6595708+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.button输入Key_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button输入Key_Click</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T15:23:10.9781315+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.GetStationListCount#int#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetStationListCount</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T15:22:40.3948376+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.搜索并下载照片#void#string, bool</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>搜索并下载照片</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T14:43:28.1419783+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.SearchStationInfo#List&lt;StationInfoExcerpt&gt;#string, bool</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SearchStationInfo</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T12:54:30.0687864+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.WriteLog#void#string, bool</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WriteLog</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T12:52:42.1661002+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.ReadIniConfig#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ReadIniConfig</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-04T12:15:32.9363737+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmChinaTowerdownload</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:43:41.8858184+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.button输入Key_Click_1#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>button输入Key_Click_1</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:43:29.1425796+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.fileSelectControl存放路径_Selected#void#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>fileSelectControl存放路径_Selected</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:43:12.7709277+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmChinaTowerdownload</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:37:23.439882+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.SetButtonText#void#Button, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetButtonText</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:37:17.0384783+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Managers.WebBrowserCookieManagerV2.SetCookiesInternalAsync#Task#WebView2, CookieData</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>SetCookiesInternalAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Managers.WebBrowserCookieManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:54:11.3417734+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Managers\WebBrowserCookieManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerdownload.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerdownload.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:42:46.07224+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload.Dispose#void#bool</ID><ImageSource>img\tvi\x_method_override-s_protected-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>Dispose</ItemName><ItemPath>HyAssistant.frmChinaTowerdownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerdownload.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:42:37.6949502+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.frmChinaTowerdownload</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>frmChinaTowerdownload</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerdownload.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-03T23:42:33.8402779+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\frmChinaTowerdownload.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\ChinaTowerDownload.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.ChinaTowerDownload.ButtonLogin_Click#void#object, EventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ButtonLogin_Click</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.ChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T12:14:25.6049572+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.ChinaTowerDownload.ExtractAuthenticationInfo#AuthenticationInfo#Dictionary&lt;string, string&gt;</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ExtractAuthenticationInfo</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.ChinaTowerDownload</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName><TimeStamp>2025-07-09T12:14:22.7628582+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\ChinaTowerDownload.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaUIPermissionManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.HaUIPermissionManager.RefreshMenuPermissionsInternalAsync#Task#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>RefreshMenuPermissionsInternalAsync</ItemName><ItemPath>HyAssistant.HaUIPermissionManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:04:28.4711557+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.HaUIPermissionManager</ID><ImageSource>img\tvi\y_class-s_internal-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>HaUIPermissionManager</ItemName><ItemPath>HyAssistant</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaUIPermissionManager.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:04:04.22869+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HaUIPermissionManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Program.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.Program.Mainstatic##void#</ID><ImageSource>img\tvi\x_method-s_private-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>Main</ItemName><ItemPath>HyAssistant.Program</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Program.cs</ProjectItemFileName><TimeStamp>2025-07-17T10:10:29.185019+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Program.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\IPhotoRepository.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.IPhotoRepository.InsertPhotosAsync#Task&lt;int&gt;#List&lt;PhotoInfoExcerpt&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>InsertPhotosAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.IPhotoRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName><TimeStamp>2025-07-05T21:19:48.486937+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\IPhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\PhotoRepository.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.PhotoRepository.PhotoExistsInternalAsync#Task&lt;bool&gt;#SQLiteConnection, string, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PhotoExistsInternalAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.PhotoRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName><TimeStamp>2025-07-05T21:20:09.9735904+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.PhotoRepository.InsertPhotosAsync#Task&lt;int&gt;#List&lt;PhotoInfoExcerpt&gt;</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>InsertPhotosAsync</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data.PhotoRepository</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName><TimeStamp>2025-07-05T20:27:38.8807151+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.ChinaTowerDownload.Data.PhotoRepository</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>PhotoRepository</ItemName><ItemPath>HyAssistant.ChinaTowerDownload.Data</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName><TimeStamp>2025-07-05T20:27:28.1990082+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\ChinaTowerDownload\Data\PhotoRepository.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfigForm.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.TabConfigForm.CloneConfig#WebBrowserTabConfig#WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CloneConfig</ItemName><ItemPath>HyAssistant.TabConfigForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfigForm.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:22:15.0310468+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\TabConfigForm.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.GetFormattedUrl#string#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GetFormattedUrl</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-15T00:00:01.1982305+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.UpdateCookiePathFileAsync#Task#WebView2, WebBrowserTabConfig, Dictionary&lt;string, string&gt;, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>UpdateCookiePathFileAsync</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:52:30.87627+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.GetCurrentTabData#TabData#</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetCurrentTabData</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:51:59.8866466+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.GetSectionIdByTabName#string#string</ID><ImageSource>img\tvi\x_method-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>GetSectionIdByTabName</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:51:55.8691027+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.WebView_WebResourceRequested#void#object, CoreWebView2WebResourceRequestedEventArgs, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebView_WebResourceRequested</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:49:33.5007946+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.WebView_NavigationStarting#void#object, CoreWebView2NavigationStartingEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WebView_NavigationStarting</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:49:15.9480164+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.WebView_NavigationCompleted#void#object, CoreWebView2NavigationCompletedEventArgs</ID><ImageSource>img\tvi\x_method_eventhandler-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>WebView_NavigationCompleted</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:49:10.5181847+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.RegisterWebResourceRequestedHandler#void#WebView2, string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>RegisterWebResourceRequestedHandler</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T23:49:03.3179168+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.ConfigureWebView2Internal#void#WebView2, WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ConfigureWebView2Internal</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:58:21.0147814+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserTabManager.IsApiRequest#bool#string, string, WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IsApiRequest</ItemName><ItemPath>HyAssistant.WebBrowserTabManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:52:43.7443133+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserTabManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.Designer.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.CookieManagerForm.InitializeComponent#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeComponent</ItemName><ItemPath>HyAssistant.CookieManagerForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName><TimeStamp>2025-07-10T16:02:24.5374668+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\CookieManagerForm.Designer.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\IConfigReader.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.NewFolder.IConfigReader</ID><ImageSource>img\tvi\y_interface-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>IConfigReader</ItemName><ItemPath>HyAssistant.NewFolder</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\IConfigReader.cs</ProjectItemFileName><TimeStamp>2025-07-14T21:07:39.6205268+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\NewFolder\IConfigReader.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistantLicenseManager.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.HyAssistantLicenseManager.LoadUserGroupsFromConfigstatic##string[]#string</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>LoadUserGroupsFromConfig</ItemName><ItemPath>HyAssistant.HyAssistantLicenseManager</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistantLicenseManager.cs</ProjectItemFileName><TimeStamp>2025-06-28T15:38:52.0978775+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistantLicenseManager.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\RetryStrategyManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.RetryStrategyManagerV2.CreateWebView2Strategystatic##RetryStrategy#int</ID><ImageSource>img\tvi\x_method-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CreateWebView2Strategy</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.RetryStrategyManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T21:09:16.3866569+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\RetryStrategyManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserV2Constants.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserV2Constants.CONFIG_FILE_NAME</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CONFIG_FILE_NAME</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserV2Constants</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:19:18.6300023+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserV2Constants</ID><ImageSource>img\tvi\y_class-s_public-i_static</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebBrowserV2Constants</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:14:16.5005774+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.WebBrowserV2Constants.CONFIG_FILE_V2</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CONFIG_FILE_V2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.WebBrowserV2Constants</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName><TimeStamp>2025-07-16T21:13:59.3428388+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\WebBrowserV2Constants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.CookieFunctionalityTesterV2.TestJsonFormatValidation#Task&lt;CookieTestResult&gt;#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>TestJsonFormatValidation</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.CookieFunctionalityTesterV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T20:38:32.2609984+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\CookieFunctionalityTesterV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.GenerateAndCopyConfigurationAsync#Task#WebView2, WebBrowserTabConfig</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>GenerateAndCopyConfigurationAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName><TimeStamp>2025-07-16T20:07:00.790385+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.ProcessClipboardDataAsync#Task#WebView2</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ProcessClipboardDataAsync</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName><TimeStamp>2025-07-16T20:06:59.9155311+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ID><ImageSource>img\tvi\y_class-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>WebBrowserV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName><TimeStamp>2025-07-16T18:03:20.1856164+08:00</TimeStamp></GlobalHistoryDataItem><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Core.WebBrowserV2.CleanJsonStringV2#string#string</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>CleanJsonStringV2</ItemName><ItemPath>HyAssistant.WebBrowserV2.Core.WebBrowserV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName><TimeStamp>2025-07-16T17:32:40.0746834+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Core\WebBrowserCookieOperations.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.MainForm.InitializeNewFolderFeature#void#</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>InitializeNewFolderFeature</ItemName><ItemPath>HyAssistant.MainForm</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.cs</ProjectItemFileName><TimeStamp>2025-07-13T20:52:26.7660555+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\Main.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.OperationRecoveryManagerV2.ShouldRetry#bool#Exception, int, int</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShouldRetry</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.OperationRecoveryManagerV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T22:40:22.4817389+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\OperationRecoveryManagerV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConstants.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserConstants.CONFIG_FILE</ID><ImageSource>img\tvi\x_const-s_public-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>true</IsPublic><ItemName>CONFIG_FILE</ItemName><ItemPath>HyAssistant.WebBrowserConstants</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConstants.cs</ProjectItemFileName><TimeStamp>2025-07-16T20:58:06.1982244+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowser\WebBrowserConstants.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW><a:KeyValueOfstringProjectItemDatajRe1r2YW><a:Key>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</a:Key><a:Value><DataItems><GlobalHistoryDataItem><ID>HyAssistant.WebBrowserV2.Utils.BoundaryAndExceptionTestExecutorV2.ShowTestResults#void#BoundaryTestSummary</ID><ImageSource>img\tvi\x_method-s_private-i_instance</ImageSource><IsPinned>false</IsPinned><IsPublic>false</IsPublic><ItemName>ShowTestResults</ItemName><ItemPath>HyAssistant.WebBrowserV2.Utils.BoundaryAndExceptionTestExecutorV2</ItemPath><ProjectFullName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\HyAssistant.csproj</ProjectFullName><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName><TimeStamp>2025-07-15T20:38:17.4694781+08:00</TimeStamp></GlobalHistoryDataItem></DataItems><ProjectItemFileName>D:\HyDevelop\HyHelper\HyHelper\HyAssistant\WebBrowserV2\Utils\BoundaryAndExceptionTestExecutorV2.cs</ProjectItemFileName></a:Value></a:KeyValueOfstringProjectItemDatajRe1r2YW></ProjectGlobalHistoryData><ProjectName>HyAssistant</ProjectName></ProjectData>