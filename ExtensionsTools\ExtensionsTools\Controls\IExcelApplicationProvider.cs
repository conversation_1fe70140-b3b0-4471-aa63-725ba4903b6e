using Microsoft.Office.Interop.Excel;

namespace ET.Controls
{
    /// <summary>
    /// Excel应用程序提供者接口 用于在不同的Excel集成环境中提供Excel应用程序实例
    /// </summary>
    public interface IExcelApplicationProvider
    {
        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        Application GetExcelApplication();
    }

    /// <summary>
    /// 默认的Excel应用程序提供者 通过COM互操作获取活动的Excel实例
    /// </summary>
    public class DefaultExcelApplicationProvider : IExcelApplicationProvider
    {
        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                // 通过COM互操作获取活动的Excel实例
                return (Application)System.Runtime.InteropServices.Marshal.GetActiveObject("Excel.Application");
            }
            catch
            {
                return null;
            }
        }
    }

    /// <summary>
    /// VSTO环境的Excel应用程序提供者 需要在VSTO项目中实现具体的获取逻辑
    /// </summary>
    public class VSTOExcelApplicationProvider : IExcelApplicationProvider
    {
        private readonly System.Func<object> _applicationGetter;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="applicationGetter">获取Excel应用程序的委托（返回object以避免跨程序集边界的互操作类型问题）</param>
        public VSTOExcelApplicationProvider(System.Func<object> applicationGetter)
        {
            _applicationGetter = applicationGetter;
        }

        /// <summary>
        /// 获取Excel应用程序实例
        /// </summary>
        /// <returns>Excel应用程序实例，如果获取失败则返回null</returns>
        public Application GetExcelApplication()
        {
            try
            {
                var app = _applicationGetter?.Invoke();
                return app as Application;
            }
            catch
            {
                return null;
            }
        }
    }
}